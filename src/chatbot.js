/**
 * CaseBuilder Chatbot Module
 *
 * This module provides AI-powered chatbot functionality using OpenAI's API.
 */

// Import required dependencies
const OpenAI = require('openai');

/**
 * Initialize the OpenAI client with the provided API key
 * @param {string} apiKey - The OpenAI API key
 * @returns {OpenAI} - The initialized OpenAI client
 */
function initializeOpenAI(apiKey) {
  if (!apiKey) {
    throw new Error('OpenAI API key is required');
  }

  return new OpenAI({
    apiKey: apiKey
  });
}

/**
 * Process a chat message and generate a response using OpenAI
 * @param {OpenAI} openai - The initialized OpenAI client
 * @param {string} message - The user's message
 * @param {Array} history - Previous conversation history (optional)
 * @returns {Promise<Object>} - The AI response
 */
async function processChatMessage(openai, message, history = []) {
  try {
    // Prepare the conversation history in the format expected by OpenAI
    const messages = [
      {
        role: 'system',
        content: 'You are a helpful assistant for CaseBuilder, a legal tech platform that helps personal injury attorneys generate medical summaries, analyze police reports, and draft demand letters. Provide concise, accurate information about Case<PERSON><PERSON><PERSON>\'s services and how they can help attorneys streamline their workflow. Focus on the key benefits: AI-powered case analysis, smart damages calculation, visual evidence integration, and automated demand letter generation. Be professional but conversational, and emphasize how CaseBuilder saves time and helps attorneys build stronger cases. ONLY when users specifically express interest in trying CaseBuilder or ask about pricing, demos, or trials, then enthusiastically promote our free trial by directing them to click the "Start Free Trial" button on the website. DO NOT provide any URLs or links - only refer to the buttons on the page. DO NOT mention the free trial in every response - only when directly relevant to the user\'s question. If they specifically ask about a demo instead, suggest scheduling one through the "Schedule Your Demo" button. Keep responses under 3-4 sentences when possible.'
      },
      ...history,
      {
        role: 'user',
        content: message
      }
    ];

    // Call the OpenAI API to generate a response
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: messages,
      max_tokens: 500,
      temperature: 0.7,
    });

    // Extract and return the response
    return {
      success: true,
      message: completion.choices[0].message.content,
      usage: completion.usage
    };
  } catch (error) {
    console.error('Error processing chat message:', error);
    return {
      success: false,
      message: 'An error occurred while processing your message',
      error: error.message
    };
  }
}

module.exports = {
  initializeOpenAI,
  processChatMessage
};
