<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Data Security Policy – CaseBuilderAI</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom text color */
    .custom-blue-text {
      color: #00BFD4 !important;
    }
    .custom-blue-text:hover {
      color: #00E1FF !important;
      text-shadow: 0 0 8px rgba(0, 191, 212, 0.5);
    }

    /* Custom background color */
    .custom-blue-bg {
      background-color: #00BFD4 !important;
    }
    .custom-blue-bg:hover {
      background-color: #00E1FF !important;
      box-shadow: 0 0 8px rgba(0, 191, 212, 0.5);
    }

    /* Custom border color */
    .custom-blue-border {
      border-color: #00BFD4 !important;
    }
    .custom-blue-border:hover {
      border-color: #00E1FF !important;
      box-shadow: 0 0 8px rgba(0, 191, 212, 0.5);
    }
  </style>
</head>
<body class="bg-black text-white">
  <!-- ============================= -->
  <!--        NAVIGATION BAR        -->
  <!-- ============================= -->


  <!-- ============================= -->
  <!--        SECURITY HEADER       -->
  <!-- ============================= -->
  <section class="text-center px-6 py-12 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(0,191,212,0.1),transparent_60%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.05">
      <!-- Shield Shape -->
      <div class="absolute top-10 right-1/4 w-40 h-50 parallax-element" data-speed="-0.1" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); transform: scaleY(1.2); border: 2px solid rgba(0,191,212,0.2);"></div>

      <!-- Lock Circles -->
      <div class="absolute bottom-10 left-1/4 w-20 h-20 rounded-full border-2 border-[#00BFD4]/30 parallax-element float-animation" data-speed="0.15"></div>
      <div class="absolute bottom-20 left-1/4 w-10 h-16 border-t-2 border-l-2 border-r-2 border-[#00BFD4]/30 rounded-t-full parallax-element" data-speed="0.15"></div>

      <!-- Data Flow Lines -->
      <div class="absolute top-1/3 left-0 right-0 h-[2px] parallax-element" data-speed="0.05" style="background: repeating-linear-gradient(90deg, rgba(0,191,212,0.3), rgba(0,191,212,0.3) 10px, transparent 10px, transparent 20px);"></div>

      <!-- Binary Pattern -->
      <div class="absolute right-20 top-20 opacity-30 parallax-element" data-speed="-0.2" style="font-family: monospace; font-size: 14px; color: #00BFD4; font-weight: bold;">
        <div>10110101</div>
        <div>01001010</div>
        <div>11010010</div>
        <div>00101101</div>
      </div>
    </div>

    <div class="relative z-10">
      <h1 class="text-3xl font-bold mb-2 text-white">Data Security Policy</h1>
      <p class="text-gray-400 max-w-2xl mx-auto">
        Last updated: 03/31/2025
      </p>
    </div>
  </section>

  <!-- ============================= -->
  <!--       SECURITY CONTENT       -->
  <!-- ============================= -->
  <section class="bg-gradient-to-b from-gray-900 via-gray-850 to-gray-800 px-6 py-8 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,rgba(0,191,212,0.15),transparent_70%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.04">
      <!-- Hexagons -->
      <div class="absolute top-40 right-20 w-60 h-60 parallax-element" data-speed="-0.12" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); border: 2px solid rgba(0,191,212,0.2);"></div>

      <!-- Diagonal Lines -->
      <div class="absolute top-20 left-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform rotate-45 parallax-element" data-speed="0.1"></div>
      <div class="absolute bottom-40 right-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform -rotate-45 parallax-element" data-speed="-0.08"></div>

      <!-- Dots Pattern -->
      <div class="absolute right-0 bottom-0 w-1/2 h-1/2 parallax-element" data-speed="0.03" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 20px 20px;"></div>

      <!-- Security Grid -->
      <div class="absolute left-0 top-1/3 w-1/3 h-1/3 parallax-element" data-speed="0.03" style="background-image: linear-gradient(rgba(0,191,212,0.15) 1px, transparent 1px), linear-gradient(90deg, rgba(0,191,212,0.15) 1px, transparent 1px); background-size: 20px 20px;"></div>

      <!-- Encryption Key -->
      <div class="absolute left-20 bottom-40 opacity-30 parallax-element" data-speed="-0.1" style="font-family: monospace; font-size: 12px; color: #00BFD4; font-weight: bold;">
        <div>A7F9B3C2</div>
        <div>E5D8G6H1</div>
        <div>J4K2L0M3</div>
      </div>
    </div>
    <div class="max-w-4xl mx-auto prose prose-invert prose-blue prose-lg text-gray-300">
      <p>
        CaseBuilderAI, LLC ("CaseBuilder"), protects user information through rigorous technical and organizational safeguards. Although the application stores only essential login data and processes documents ephemerally, we follow controls that meet or exceed industry standards for confidentiality, integrity, and availability.
      </p>

      <h2 class="text-xl font-bold mt-8">1. Secure Data Transmission</h2>
      <ul>
        <li>Encryption in transit: All traffic between the user's browser and CaseBuilder servers is protected with HTTPS / TLS 1.2 or 1.3.</li>
        <li>Encryption at rest: Any temporary data written inside the processing container is encrypted with AES-256.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">2. Local Processing & Zero Retention</h2>
      <p>
        CaseBuilder is built with a privacy-first architecture that ensures all document handling happens securely and locally within the user's browser. No files are ever transmitted to external servers or stored in the cloud.
      </p>

      <h3 class="text-lg font-semibold">2.1 Browser-Based Document Processing</h3>
      <p>
        Uploaded documents (PDFs, images, etc.) are processed entirely within the user's browser environment.
      </p>
      <ul>
        <li>Files never leave the local session or pass through CaseBuilder servers.</li>
        <li>Processing is powered by client-side technologies such as WebAssembly and JavaScript.</li>
        <li>No backend or cloud-based computation is involved in document analysis.</li>
      </ul>

      <h3 class="text-lg font-semibold">2.2 Zero Retention Architecture</h3>
      <p>
        Documents reside temporarily in the browser's memory only during the processing session.
      </p>
      <ul>
        <li>Once results are returned, the temporary processing container is automatically destroyed.</li>
        <li>No persistent copies are stored — not in databases, logs, or any form of disk storage.</li>
      </ul>

      <h3 class="text-lg font-semibold">2.3 Encryption in Transit</h3>
      <p>
        Although files are not transmitted, all communication with CaseBuilder (e.g., authentication, API model access) is encrypted via HTTPS (TLS 1.2/1.3).
      </p>

      <h3 class="text-lg font-semibold">2.4 HIPAA Business Associate Agreement (BAA) Provided</h3>
      <p>
        CaseBuilder executes a Business Associate Agreement (BAA) with all firms using the platform, at no additional cost. Our infrastructure is designed to fully support HIPAA compliance by ensuring that no Protected Health Information (PHI) is ever stored, transmitted, or retained.
      </p>

      <h2 class="text-xl font-bold mt-8">3. Secure Password Storage</h2>
      <ul>
        <li>System-generated credentials: Users do not create passwords; they are randomly generated by the platform.</li>
        <li>Bcrypt + salt: Password hashes are stored with bcrypt and unique salts.</li>
        <li>Multi-Factor Authentication (MFA): Supported and strongly recommended.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">4. Protection Against Brute-Force Attacks</h2>
      <ul>
        <li>Temporary account lockout after five failed login attempts.</li>
        <li>Email alerts for suspicious logins from new devices or locations.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">5. Access Control & User Permissions</h2>
      <ul>
        <li>Role-Based Access Control (RBAC) enforces least-privilege access.</li>
        <li>Permissions are reviewed during quarterly security audits.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">6. Secure Server Configuration</h2>
      <ul>
        <li>Hardened images (default passwords removed, unused ports closed).</li>
        <li>Continuous patching of OS and runtime dependencies.</li>
        <li>Intrusion-Detection System (IDS) monitors anomalies and raises real-time alerts.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">7. Monitoring, Logging & Incident Response</h2>
      <ul>
        <li>Immutable security logs record failed logins, access attempts, and API calls.</li>
        <li>Logs are reviewed regularly by the security team.</li>
        <li>Incident response: Users are notified within 24 hours of any confirmed breach. Root-cause analysis and remediation begin immediately.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">8. Vulnerability Management & Security Assessments</h2>
      <ul>
        <li>Quarterly internal vulnerability scans.</li>
        <li>Annual third-party penetration tests; attestation letter available on request.</li>
        <li>Findings are prioritized and remediated according to severity SLAs.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">9. Compliance with Data-Protection Regulations</h2>
      <ul>
        <li>CaseBuilder adheres to GDPR and CCPA requirements.</li>
        <li>Users may request access, correction, or deletion of personal data by contacting support.</li>
      </ul>

      <h2 class="text-xl font-bold mt-8">Contact Information</h2>
      <p>
        If you have any questions about this Data Security Policy, please contact us:
      </p>
      <ul class="list-none pl-0">
        <li><strong>Email:</strong> <a href="mailto:<EMAIL>" class="custom-blue-text hover:underline"><EMAIL></a></li>
        <li><strong>Phone:</strong> <a href="tel:+18667513005" class="custom-blue-text hover:underline">+****************</a></li>
        <li><strong>Address:</strong> 131 Continental Drive, Suite 305, Newark, Delaware 19713</li>
      </ul>

      <p>
        For additional security questions or to request a BAA, please reach out using the contact details above.
      </p>
    </div>
  </section>

  <!-- ============================= -->
  <!--       SECURITY SUMMARY       -->
  <!-- ============================= -->
  <section class="bg-gradient-to-br from-gray-900 via-gray-850 to-gray-900 py-16 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_10%_50%,rgba(0,191,212,0.08),transparent_70%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.06">
      <!-- Shield Shapes -->
      <div class="absolute top-20 right-40 w-40 h-50 parallax-element" data-speed="-0.1" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); transform: scaleY(1.2); border: 2px solid rgba(0,191,212,0.2);"></div>

      <!-- Lock Circles -->
      <div class="absolute bottom-20 left-20 w-30 h-30 rounded-full border-2 border-[#00BFD4]/30 parallax-element float-animation" data-speed="0.15"></div>
      <div class="absolute bottom-30 left-30 w-10 h-20 border-t-2 border-l-2 border-r-2 border-[#00BFD4]/30 rounded-t-full parallax-element" data-speed="0.15"></div>

      <!-- Data Flow Lines -->
      <div class="absolute top-1/3 left-0 right-0 h-[2px] parallax-element" data-speed="0.05" style="background: repeating-linear-gradient(90deg, rgba(0,191,212,0.3), rgba(0,191,212,0.3) 10px, transparent 10px, transparent 20px);"></div>
      <div class="absolute bottom-1/4 left-0 right-0 h-[2px] parallax-element" data-speed="-0.05" style="background: repeating-linear-gradient(90deg, rgba(0,191,212,0.3), rgba(0,191,212,0.3) 10px, transparent 10px, transparent 20px);"></div>

      <!-- Binary Pattern -->
      <div class="absolute right-20 top-20 opacity-30 parallax-element" data-speed="-0.2" style="font-family: monospace; font-size: 14px; color: #00BFD4; font-weight: bold;">
        <div>10110101</div>
        <div>01001010</div>
        <div>11010010</div>
        <div>00101101</div>
      </div>
    </div>

    <div class="max-w-6xl mx-auto px-6 relative z-10">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-white">Your Data. Protected.</h2>
        <p class="text-lg text-gray-300 max-w-3xl mx-auto mt-4">
          CaseBuilder's security-first approach ensures your sensitive client information is always protected with industry-leading safeguards.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
        <div class="bg-gray-800/50 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4]/50 transition-all duration-300">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-[#00BFD4]/20 flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-[#00BFD4]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white">Zero Retention</h3>
          </div>
          <p class="text-gray-300">Your documents are never stored permanently. After processing, all data is automatically purged.</p>
        </div>

        <div class="bg-gray-800/50 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4]/50 transition-all duration-300">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-[#00BFD4]/20 flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-[#00BFD4]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white">HIPAA Ready</h3>
          </div>
          <p class="text-gray-300">Our platform is designed to meet healthcare privacy standards with BAA available at no extra cost.</p>
        </div>

        <div class="bg-gray-800/50 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4]/50 transition-all duration-300">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 rounded-full bg-[#00BFD4]/20 flex items-center justify-center mr-4">
              <svg class="w-6 h-6 text-[#00BFD4]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white">Transparent Policies</h3>
          </div>
          <p class="text-gray-300">We provide clear documentation of our security practices and are always available to answer questions.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- ============================= -->
  <!--            FOOTER            -->
  <!-- ============================= -->
  <footer class="bg-gray-900 text-white py-10">
    <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">

      <!-- Brand -->
      <div>
        <div class="mb-3">
          <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-150" />
        </div>
      </div>

      <!-- Navigation -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Navigation</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="index.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Home</a></li>
          <li><a href="pricing.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Pricing</a></li>
          <li><a href="faq.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">FAQ</a></li>
          <li><a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact</a></li>
        </ul>
      </div>

      <!-- Legal -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Legal</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a></li>
          <li><a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a></li>
          <li><a href="security.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Data Security Policy</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Contact</h3>
        <p class="text-sm text-gray-400 mb-1">131 Continental Dr Suite 305,</p>
        <p class="text-sm text-gray-400 mb-3">Newark, DE 19713</p>
        <p class="text-sm text-gray-400 mb-1">Phone: <a href="tel:+18667513005" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">+****************</a></p>
        <p class="text-sm text-gray-400 mb-3">Email: <a href="mailto:<EMAIL>" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded"><EMAIL></a></p>
        <div class="flex space-x-4">
          <a href="https://www.facebook.com/people/CaseBuilderai/61560221212659" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
            </svg>
          </a>
          <a href="https://www.instagram.com/casebuilderai/" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
            </svg>
          </a>
          <a href="https://www.youtube.com/channel/UCpL3DBmoSk5rpDUBnmJccqQ" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
            </svg>
          </a>
        </div>
      </div>

    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 mt-10 pt-6 text-center text-gray-500 text-sm">
      <p>&copy; 2024 CaseBuilderAI, LLC — All rights reserved.</p>
      <div class="mt-2">
        <a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a> •
        <a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a> •
        <a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact Us</a>
      </div>
    </div>
  </footer>

  <!-- Add keyframes for animations -->
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .float-animation {
      animation: float 6s ease-in-out infinite;
    }
  </style>

  <!-- Parallax Effect Script -->
  <script>
    // Parallax Effect
    window.addEventListener('scroll', function() {
      const parallaxSections = document.querySelectorAll('.parallax-section');

      parallaxSections.forEach(section => {
        const distance = window.scrollY - section.offsetTop;
        const isInView = window.scrollY + window.innerHeight > section.offsetTop &&
                         window.scrollY < section.offsetTop + section.offsetHeight;

        if (isInView) {
          // Parallax for layers
          const layers = section.querySelectorAll('.parallax-layer');
          layers.forEach(layer => {
            const speed = layer.getAttribute('data-speed') || 0.05;
            layer.style.transform = `translateY(${distance * speed}px)`;
          });

          // Parallax for individual elements
          const elements = section.querySelectorAll('.parallax-element');
          elements.forEach(element => {
            const speed = element.getAttribute('data-speed') || 0.1;
            const translateY = distance * speed;
            element.style.transform = element.style.transform
              ? element.style.transform.replace(/translateY\([^)]+\)/, '') + ` translateY(${translateY}px)`
              : `translateY(${translateY}px)`;
          });
        }
      });
    });

    // Add animation to security cards (excluding footer)
    document.addEventListener('DOMContentLoaded', function() {
      // Specifically target only the cards in the main content, not in the footer
      const securityCards = document.querySelectorAll('.parallax-section .grid-cols-1 > div');

      // Function to check if element is in viewport
      function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
          rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.bottom >= 0
        );
      }

      // Initial check for elements in viewport
      function checkElements() {
        securityCards.forEach(function(card, index) {
          // Skip any elements that are inside the footer
          if (card.closest('#legal-footer')) return;

          if (isInViewport(card) && !card.classList.contains('animated')) {
            setTimeout(function() {
              card.classList.add('animated');
              card.style.animation = 'fadeInUp 0.8s ease forwards';

              // Add glow effect to the icon
              const icon = card.querySelector('.rounded-full');
              if (icon) {
                setTimeout(() => {
                  icon.classList.add('glow-effect');
                }, 300);
              }
            }, index * 200);
          }
        });
      }

      // Check on scroll
      window.addEventListener('scroll', checkElements);

      // Initial check
      checkElements();

      // Add keyframes for animations
      if (!document.querySelector('#animations-keyframes')) {
        const style = document.createElement('style');
        style.id = 'animations-keyframes';
        style.textContent = `
          /* Class for persistent glow effect */
          .glow-effect {
            animation: glowPulse 3s infinite;
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translate3d(0, 40px, 0);
            }
            to {
              opacity: 1;
              transform: translate3d(0, 0, 0);
            }
          }

          @keyframes glowPulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(0, 191, 212, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0); }
          }
        `;
        document.head.appendChild(style);
      }
    });
  </script>
</body>
</html>
