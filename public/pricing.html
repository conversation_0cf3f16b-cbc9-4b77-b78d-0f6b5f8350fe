<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Pricing – CaseBuilder</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom text color */
    .custom-blue-text {
      color: #00BFD4 !important;
    }
    .custom-blue-text:hover {
      color: #00d0e7 !important;
    }

    /* Custom background color */
    .custom-blue-bg {
      background-color: #00BFD4 !important;
    }
    .custom-blue-bg:hover {
      background-color: #00d0e7 !important;
    }

    /* Custom border color */
    .custom-blue-border {
      border-color: #00BFD4 !important;
    }
    .custom-blue-border:hover {
      border-color: #00d0e7 !important;
    }

    /* Custom gradient */
    .custom-blue-gradient {
      background: linear-gradient(to right, #00BFD4, #00a8bb) !important;
    }
  </style>
</head>
<body class="bg-gray-900 text-white">
  <!-- ============================= -->
  <!--        NAVIGATION BAR        -->
  <!-- ============================= -->
  <nav id="navbar" class="flex justify-between items-center px-6 py-0 shadow-xl bg-gray-900 text-white border-b border-gray-800 sticky top-0 z-50 transition-transform duration-300">
    <div class="flex items-center">
      <a href="index.html">
        <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-180" />
      </a>
    </div>


    <!-- Desktop menu -->
    <ul class="flex gap-6 text-sm font-medium">
      <li>
        <a
          href="https://app.casebuilder.ai"
          target="_blank"
          class="custom-blue-bg text-white px-6 py-3 rounded-lg text-base font-medium hover:custom-blue-bg shadow-md hover:shadow-[0_0_15px_rgba(0,191,212,0.5)]"
        >Log In</a>
      </li>
    </ul>
  </nav>


  <!-- Add JavaScript for mobile menu toggle and navbar scroll behavior -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {

      // Navbar scroll behavior
      const navbar = document.getElementById('navbar');
      let lastScrollTop = 0;

      window.addEventListener('scroll', function() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
          // Scrolling down & past threshold
          navbar.style.transform = 'translateY(-100%)';
        } else {
          // Scrolling up or at the top
          navbar.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
      });
    });
  </script>

  <!-- ============================= -->
  <!--         PRICING HERO         -->
  <!-- ============================= -->
  <section class="hero-section text-center px-6 py-12 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(0,191,212,0.1),transparent_60%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.05">
      <!-- Circles -->
      <div class="absolute top-20 left-10 w-64 h-64 rounded-full border-2 border-[#00BFD4]/40 transform rotate-45 parallax-element" data-speed="0.15"></div>
      <div class="absolute top-40 left-20 w-32 h-32 rounded-full border-2 border-[#00BFD4]/30 parallax-element" data-speed="-0.1"></div>

      <!-- Lines -->
      <div class="absolute top-0 right-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="0.08"></div>
      <div class="absolute bottom-0 left-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="-0.12"></div>

      <!-- Dollar Signs for Pricing -->
      <div class="absolute bottom-20 right-20 text-[#00BFD4]/30 text-6xl font-bold parallax-element" data-speed="0.2">$</div>
      <div class="absolute top-40 right-40 text-[#00BFD4]/20 text-4xl font-bold parallax-element" data-speed="-0.15">$</div>

      <!-- Grid Pattern -->
      <div class="absolute inset-0 parallax-element" data-speed="0.02" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 30px 30px;"></div>
    </div>

    <div class="relative z-10">
      <h2 class="text-3xl font-bold mb-2 text-white">Pricing</h2>
      <p class="text-gray-300 max-w-2xl mx-auto">
        No subscriptions. No limits. Just pure AI-driven efficiency at your fingertips.
      </p>

      <div class="bg-gradient-to-br from-gray-800 to-gray-850 rounded-lg p-4 mt-6 max-w-lg mx-auto text-left shadow-md border border-[#00BFD4]/50 hover:border-[#00BFD4] transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.3)]">
      <div class="flex items-center mb-3">
        <span class="custom-blue-bg text-white text-xs px-2 py-1 rounded-full mr-2">LIMITED TIME</span>
        <h3 class="text-lg font-semibold text-white">
          Try it for only <span class="line-through text-gray-400">$19.99</span> $9.99 per analysis
        </h3>
      </div>
      <p class="text-sm text-gray-300 mb-4">(Limited time offer — tokens never expire)</p>
      <ul class="space-y-2 text-sm text-gray-300">
        <li class="flex items-start">
          <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span><strong>Flexible access:</strong> Buy tokens only when you need them</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span><strong>Instant results:</strong> Use any tool and get output in seconds</span>
        </li>
        <li class="flex items-start">
          <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span><strong>Pay-as-you-go:</strong> No subscriptions, no contracts</span>
        </li>
      </ul>
      <a href="https://buy.stripe.com/cN24gZd7Offbfks6oT" target="_blank" class="mt-5 custom-blue-bg text-white px-6 py-3 rounded-lg hover:bg-opacity-90 transition-all duration-300 flex items-center inline-flex shadow-md font-medium hover:shadow-[0_0_15px_rgba(0,191,212,0.5)] glow-effect">
        <span class="mr-2">Buy Tokens</span>
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
        </svg>
      </a>
    </div>
  </section>

  <!-- ============================= -->
  <!--        PLAN COMPARISON       -->
  <!-- ============================= -->
  <section class="plan-comparison bg-gradient-to-t from-gray-900 via-gray-850 to-gray-800 px-6 py-12 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_70%_70%,rgba(0,191,212,0.1),transparent_60%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.04">
      <!-- Hexagons -->
      <div class="absolute top-40 right-20 w-60 h-60 parallax-element" data-speed="-0.12" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); border: 2px solid rgba(0,191,212,0.2);"></div>
      <div class="absolute bottom-60 left-10 w-40 h-40 parallax-element" data-speed="0.18" style="background: linear-gradient(127deg, rgba(0,191,212,0.15), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); transform: rotate(30deg);"></div>

      <!-- Pricing Elements -->
      <div class="absolute top-1/4 right-1/3 text-[#00BFD4]/20 text-7xl font-bold parallax-element float-animation" data-speed="0.15">$</div>
      <div class="absolute bottom-1/3 left-1/4 text-[#00BFD4]/15 text-5xl font-bold parallax-element" data-speed="-0.1">$</div>

      <!-- Diagonal Lines -->
      <div class="absolute top-20 left-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform rotate-45 parallax-element" data-speed="0.1"></div>
      <div class="absolute bottom-40 right-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform -rotate-45 parallax-element" data-speed="-0.08"></div>

      <!-- Dots Pattern -->
      <div class="absolute right-0 bottom-0 w-1/2 h-1/2 parallax-element" data-speed="0.03" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 20px 20px;"></div>
    </div>

    <div class="relative z-10">
    <div class="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-center">
      <!-- Basic Plan -->
      <div class="pricing-plan bg-gradient-to-br from-gray-800 to-gray-850 rounded-lg shadow-[0_0_15px_rgba(0,191,212,0.15)] p-6 border border-gray-700 hover:border-[#00BFD4]/30 hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2">
        <div class="bg-gradient-to-r from-gray-300 to-gray-400 -mt-6 -mx-6 p-4 mb-4 rounded-t-lg">
          <h4 class="text-xl font-bold text-gray-800">Basic — <span class="line-through text-gray-600">$449</span> $299/month</h4>
          <p class="text-gray-800 text-sm mt-2">Ideal for solo attorneys or small teams</p>
        </div>
        <ul class="mt-4 text-sm text-left space-y-3 text-gray-300 mb-6">
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span><strong>30 tokens/month</strong></span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Generate up to <strong>6 demand letters</strong></span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Access to all AI tools</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Standard support (24–48h response)</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Use your own files — no setup or training required</span>
          </li>
        </ul>
        <a href="https://buy.stripe.com/5kA4gZ1p6eb75JS6oJ" target="_blank" class="w-full custom-blue-bg text-white py-2 rounded-lg hover:bg-opacity-90 transition-all duration-300 inline-block text-center font-medium hover:shadow-[0_0_15px_rgba(0,191,212,0.5)]">
          Buy Basic
        </a>
      </div>

      <!-- Standard Plan -->
      <div class="pricing-plan bg-gradient-to-br from-gray-800 to-gray-850 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] p-6 border-2 custom-blue-border hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform scale-105 hover:-translate-y-2 z-10 relative">
        <!-- Popular badge -->
        <div class="absolute -top-3 -right-3 bg-yellow-400 text-gray-900 text-xs font-bold px-3 py-1 rounded-full shadow-md">MOST POPULAR</div>

        <div class="custom-blue-bg -mt-6 -mx-6 p-4 mb-4 rounded-t-xl">
          <h4 class="text-xl font-bold text-white">Standard — <span class="line-through text-gray-300">$899</span> $599/month</h4>
          <p class="text-white text-sm mt-2">For firms with higher caseloads and tighter deadlines</p>
        </div>
        <ul class="mt-4 text-sm text-left space-y-3 text-gray-300 mb-6">
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span><strong>60 tokens/month</strong></span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Generate up to <strong>12 demand letters</strong></span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Access to all AI tools</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Priority support (12–24h response)</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Use your own files — no setup or training required</span>
          </li>
        </ul>
        <a href="https://buy.stripe.com/00g7tb4Bi7MJ0pyfZl" target="_blank" class="w-full custom-blue-bg text-white py-3 rounded-lg hover:bg-opacity-90 transition-all duration-300 inline-block text-center font-medium shadow-md hover:shadow-[0_0_15px_rgba(0,191,212,0.5)] glow-effect">
          Buy Standard
        </a>
      </div>

      <!-- Custom Plan -->
      <div class="pricing-plan bg-gradient-to-br from-gray-800 to-gray-850 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] p-6 border border-gray-700 hover:border-[#00BFD4]/30 hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2 relative">
        <!-- Premium badge -->
        <div class="absolute -top-3 -right-3 bg-gray-800 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md border-2 border-[#00BFD4]">🛠️ CUSTOM</div>

        <div class="bg-gradient-to-r from-gray-700 to-gray-900 -mt-6 -mx-6 p-4 mb-4 rounded-t-xl">
          <h4 class="text-xl font-bold text-white">Custom Plan – Fully Tailored</h4>
          <p class="text-white text-sm mt-2">Need more volume or customization? We'll build it with you.</p>
        </div>
        <ul class="mt-4 text-sm text-left space-y-3 text-gray-300 mb-6">
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span><strong>Fully customized token plans</strong></span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Customized AI tools</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Advanced support with dedicated contact</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Built-in collaboration tools for teams</span>
          </li>
          <li class="flex items-start">
            <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>Flexible billing and custom workflows</span>
          </li>
        </ul>
        <a href="contact.html" class="w-full custom-blue-bg text-white py-2 rounded-lg hover:bg-opacity-90 transition-all duration-300 inline-block text-center font-medium hover:shadow-[0_0_15px_rgba(0,191,212,0.5)]">
          Contact Us →
        </a>
      </div>
    </div>
    </div>
  </section>

  <!-- ============================= -->
  <!--            FOOTER            -->
  <!-- ============================= -->
  <footer class="bg-gray-900 text-white py-10 mt-20">
    <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">

      <!-- Brand -->
      <div>
        <div class="mb-3">
          <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-150" />
        </div>
      </div>

      <!-- Navigation -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Navigation</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="index.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Home</a></li>
          <li><a href="pricing.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Pricing</a></li>
          <li><a href="faq.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">FAQ</a></li>
          <li><a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact</a></li>
        </ul>
      </div>

      <!-- Legal -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Legal</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a></li>
          <li><a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a></li>
          <li><a href="security.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Data Security Policy</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Contact</h3>
        <p class="text-sm text-gray-400 mb-1">131 Continental Dr Suite 305,</p>
        <p class="text-sm text-gray-400 mb-3">Newark, DE 19713</p>
        <p class="text-sm text-gray-400 mb-1">Phone: <a href="tel:+18667513005" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">+****************</a></p>
        <p class="text-sm text-gray-400 mb-3">Email: <a href="mailto:<EMAIL>" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded"><EMAIL></a></p>
        <div class="flex space-x-4">
          <a href="https://www.facebook.com/people/CaseBuilderai/61560221212659" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
            </svg>
          </a>
          <a href="https://www.instagram.com/casebuilderai/" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
            </svg>
          </a>
          <a href="https://www.youtube.com/channel/UCpL3DBmoSk5rpDUBnmJccqQ" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
            </svg>
          </a>
        </div>
      </div>

    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 mt-10 pt-6 text-center text-gray-500 text-sm">
      <p>&copy; 2024 CaseBuilderAI, LLC — All rights reserved.</p>
      <div class="mt-2">
        <a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a> •
        <a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a> •
        <a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact Us</a>
      </div>
    </div>
  </footer>

  <!-- Animation Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Add keyframes and styles for animations
      if (!document.querySelector('#animations-keyframes')) {
        const style = document.createElement('style');
        style.id = 'animations-keyframes';
        style.textContent = `
          /* Class for persistent glow effect */
          .glow-effect {
            animation: glowPulse 3s infinite;
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translate3d(0, 40px, 0);
            }
            to {
              opacity: 1;
              transform: translate3d(0, 0, 0);
            }
          }

          @keyframes glowPulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(0, 191, 212, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0); }
          }
        `;
        document.head.appendChild(style);
      }

      // Animate hero section elements
      const heroTitle = document.querySelector('.hero-section h2');
      const heroSubtitle = document.querySelector('.hero-section p');
      const buyTokensCard = document.querySelector('.hero-section .bg-gradient-to-br');

      // Make Buy Tokens card visible immediately
      if (buyTokensCard) {
        buyTokensCard.style.opacity = '1';
        buyTokensCard.style.transform = 'translateY(0)';
        buyTokensCard.style.boxShadow = '0 0 15px rgba(0, 191, 212, 0.3)';
      }

      if (heroTitle) {
        heroTitle.style.opacity = '0';
        heroTitle.style.transform = 'translateY(30px)';

        setTimeout(() => {
          heroTitle.style.transition = 'opacity 1s ease, transform 1s ease';
          heroTitle.style.opacity = '1';
          heroTitle.style.transform = 'translateY(0)';
        }, 300);
      }

      if (heroSubtitle) {
        heroSubtitle.style.opacity = '0';
        heroSubtitle.style.transform = 'translateY(30px)';

        setTimeout(() => {
          heroSubtitle.style.transition = 'opacity 1s ease, transform 1s ease';
          heroSubtitle.style.opacity = '1';
          heroSubtitle.style.transform = 'translateY(0)';
        }, 600);
      }

      // Add animation to pricing cards (only the plan cards, not the Buy Tokens card)
      // Use the specific class we added to the plan cards
      const pricingCards = document.querySelectorAll('.pricing-plan');
      pricingCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(40px)';

        setTimeout(() => {
          card.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
          card.style.opacity = '1';
          card.style.transform = 'translateY(0)';
        }, 1200 + (index * 200));
      });

      // Add glow effect to buttons
      const buttons = document.querySelectorAll('.custom-blue-bg');
      buttons.forEach(button => {
        button.classList.add('glow-effect');
      });

      // Parallax Effect
      window.addEventListener('scroll', function() {
        const parallaxSections = document.querySelectorAll('.parallax-section');

        parallaxSections.forEach(section => {
          const distance = window.scrollY - section.offsetTop;
          const isInView = window.scrollY + window.innerHeight > section.offsetTop &&
                           window.scrollY < section.offsetTop + section.offsetHeight;

          if (isInView) {
            // Parallax for layers
            const layers = section.querySelectorAll('.parallax-layer');
            layers.forEach(layer => {
              const speed = layer.getAttribute('data-speed') || 0.05;
              layer.style.transform = `translateY(${distance * speed}px)`;
            });

            // Parallax for individual elements
            const elements = section.querySelectorAll('.parallax-element');
            elements.forEach(element => {
              const speed = element.getAttribute('data-speed') || 0.1;
              const translateY = distance * speed;
              element.style.transform = element.style.transform
                ? element.style.transform.replace(/translateY\([^)]+\)/, '') + ` translateY(${translateY}px)`
                : `translateY(${translateY}px)`;
            });
          }
        });
      });
    });
  </script>

  <!-- Add keyframes for animations -->
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .float-animation {
      animation: float 6s ease-in-out infinite;
    }
  </style>
</body>
</html>
