<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Free Trial – CaseBuilder</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom text color */
    .custom-blue-text {
      color: #00BFD4 !important;
    }
    .custom-blue-text:hover {
      color: #00d0e7 !important;
    }

    /* Custom background color */
    .custom-blue-bg {
      background-color: #00BFD4 !important;
    }
    .custom-blue-bg:hover {
      background-color: #00d0e7 !important;
    }

    /* Custom border color */
    .custom-blue-border {
      border-color: #00BFD4 !important;
    }
    .custom-blue-border:hover {
      border-color: #00d0e7 !important;
    }

    /* Custom ring color for form elements */
    .custom-blue-ring:focus {
      --tw-ring-color: #00BFD4 !important;
      border-color: #00BFD4 !important;
    }
  </style>
</head>
<body class="bg-gray-900 text-white">
  <!-- ============================= -->
  <!--        NAVIGATION BAR        -->
  <!-- ============================= -->
  <nav class="flex justify-between items-center px-6 py-0 shadow-xl bg-gray-900 text-white border-b border-gray-800 sticky top-0 z-50">
    <div class="flex items-center">
      <a href="index.html">
        <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-40 w-auto mr-2 my-0 py-1" />
      </a>
    </div>


    <!-- Desktop menu -->
    <ul class="flex gap-6 text-sm font-medium">
      <li>
        <a
          href="https://app.casebuilder.ai"
          target="_blank"
          class="custom-blue-bg text-white px-6 py-3 rounded-lg text-base font-medium hover:custom-blue-bg shadow-md"
        >Log In</a>
      </li>
    </ul>
  </nav>



  <!-- ============================= -->
  <!--         FREE TRIAL           -->
  <!-- ============================= -->
  <section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-16 px-6 relative overflow-hidden parallax-section">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(0,191,212,0.1),transparent_60%)]"></div>

    <!-- Geometric Elements -->
    <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.05">
      <!-- Circles -->
      <div class="absolute top-20 left-10 w-64 h-64 rounded-full border-2 border-[#00BFD4]/40 transform rotate-45 parallax-element" data-speed="0.15"></div>
      <div class="absolute top-40 left-20 w-32 h-32 rounded-full border-2 border-[#00BFD4]/30 parallax-element" data-speed="-0.1"></div>

      <!-- Lines -->
      <div class="absolute top-0 right-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="0.08"></div>
      <div class="absolute bottom-0 left-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="-0.12"></div>

      <!-- Document Icons -->
      <div class="absolute top-1/4 right-20 w-20 h-24 parallax-element" data-speed="-0.1" style="border: 2px solid rgba(0,191,212,0.3); border-radius: 2px; transform: rotate(10deg);"></div>
      <div class="absolute top-1/4 right-20 w-20 h-24 parallax-element" data-speed="-0.15" style="border: 2px solid rgba(0,191,212,0.2); border-radius: 2px; transform: rotate(-5deg); margin-right: 10px;"></div>
      <div class="absolute top-1/4 right-20 w-20 h-24 parallax-element" data-speed="-0.12" style="border: 2px solid rgba(0,191,212,0.1); border-radius: 2px; transform: rotate(3deg); margin-right: 30px;"></div>

      <!-- Grid Pattern -->
      <div class="absolute inset-0 parallax-element" data-speed="0.02" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 30px 30px;"></div>
    </div>

    <div class="max-w-5xl mx-auto relative z-10">
      <h1 class="text-3xl md:text-4xl font-bold text-white mb-4 text-center animate-title">Build Demand Letters in Minutes — Powered by AI, Proven by Results</h1>
      <p class="text-gray-300 max-w-2xl mx-auto mb-6 text-center animate-subtitle">Start your free trial now and see why top PI firms are closing million-dollar cases with CaseBuilder.</p>
      <hr class="border-gray-700 w-full max-w-3xl mx-auto mb-12">

      <div class="grid md:grid-cols-2 gap-12">
        <!-- Left Column - Form -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-lg border border-gray-700 hover:border-[#00BFD4]/30 transition-all duration-300 animate-card-left">
          <h2 class="text-xl font-semibold mb-6">Get 10 Free Tokens — No Credit Card Required</h2>

          <form id="freeTrialForm" class="space-y-4">
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-300 mb-1">First Name*</label>
              <input type="text" id="firstName" name="firstName" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 custom-blue-ring text-white" required>
            </div>

            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-300 mb-1">Last Name*</label>
              <input type="text" id="lastName" name="lastName" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 custom-blue-ring text-white" required>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email*</label>
              <input type="email" id="email" name="email" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 custom-blue-ring text-white" required>
            </div>

            <div>
              <label for="phone" class="block text-sm font-medium text-gray-300 mb-1">Phone Number*</label>
              <input type="tel" id="phone" name="phone" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 custom-blue-ring text-white" required>
            </div>

            <div>
              <label for="company" class="block text-sm font-medium text-gray-300 mb-1">Company Name*</label>
              <input type="text" id="company" name="company" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 custom-blue-ring text-white" required>
            </div>

            <div class="pt-4">
              <button type="submit"
                class="w-full inline-block custom-blue-bg text-white px-6 py-3 rounded-lg hover:custom-blue-bg transition-all duration-300 shadow-md hover:shadow-[0_0_15px_rgba(0,191,212,0.5)] hover:scale-105 focus:outline-none focus:ring-2 custom-blue-ring focus:ring-offset-2 font-medium pulse-animation">
                Get Tokens
              </button>
            </div>
          </form>
        </div>

        <!-- Right Column - Benefits -->
        <div class="text-gray-300 animate-card-right">
          <h2 class="text-xl font-semibold mb-6">End the Paperwork Pain</h2>
          <p class="mb-4">Your free trial includes 10 tokens, which you can use to:</p>

          <ul class="space-y-3 mb-8">
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span><strong class="text-white">Summarize medical records instantly</strong> — no need to read every page.</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span><strong class="text-white">Extract ICD codes and treatment plans</strong> from hundreds of pages automatically.</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span><strong class="text-white">Generate demand letter drafts</strong> ready to edit or export in Word.</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span><strong class="text-white">Get damages estimations and injury timelines</strong> instantly — no spreadsheets.</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span><strong class="text-white">Zero setup — no training, no onboarding, no steep learning curve</strong></span>
            </li>
          </ul>

          <div class="bg-gradient-to-br from-gray-800 to-gray-850 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4]/30 transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.15)]">
            <p class="text-lg font-medium mb-2">Test it on your own files — in 15 minutes or less.</p>
            <p class="text-sm">You get <b>full access</b> to all features.<br><b>No demos. No onboarding. Just results.</b></p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- ============================= -->
  <!--            FOOTER            -->
  <!-- ============================= -->
  <footer class="bg-gray-900 text-white py-10 mt-20">
    <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">

      <!-- Brand -->
      <div>
        <div class="mb-3">
          <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-150" />
        </div>
      </div>

      <!-- Navigation -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Navigation</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="index.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Home</a></li>
          <li><a href="pricing.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Pricing</a></li>
          <li><a href="faq.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">FAQ</a></li>
          <li><a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact</a></li>
        </ul>
      </div>

      <!-- Legal -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Legal</h3>
        <ul class="text-sm text-gray-400 space-y-1">
          <li><a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a></li>
          <li><a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a></li>
          <li><a href="security.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Data Security Policy</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div>
        <h3 class="text-lg font-semibold mb-2">Contact</h3>
        <p class="text-sm text-gray-400 mb-1">131 Continental Dr Suite 305,</p>
        <p class="text-sm text-gray-400 mb-3">Newark, DE 19713</p>
        <p class="text-sm text-gray-400 mb-1">Phone: <a href="tel:+18667513005" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">+****************</a></p>
        <p class="text-sm text-gray-400 mb-3">Email: <a href="mailto:<EMAIL>" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded"><EMAIL></a></p>
        <div class="flex space-x-4">
          <a href="https://www.facebook.com/people/CaseBuilderai/61560221212659" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
            </svg>
          </a>
          <a href="https://www.instagram.com/casebuilderai/" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
            </svg>
          </a>
          <a href="https://www.youtube.com/channel/UCpL3DBmoSk5rpDUBnmJccqQ" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
            </svg>
          </a>
        </div>
      </div>

    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 mt-10 pt-6 text-center text-gray-500 text-sm">
      <p>&copy; 2024 CaseBuilderAI, LLC — All rights reserved.</p>
      <div class="mt-2">
        <a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a> •
        <a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a> •
        <a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact Us</a>
      </div>
    </div>
  </footer>

  <!-- Form submission script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const freeTrialForm = document.getElementById('freeTrialForm');

      // Add success message div if it doesn't exist
      if (!document.getElementById('form-success')) {
        const successDiv = document.createElement('div');
        successDiv.id = 'form-success';
        successDiv.className = 'hidden mt-4 p-3 bg-green-100 text-green-700 rounded-md';
        successDiv.textContent = 'Thank you for signing up for the free trial! You will receive your access credentials and 10 free tokens via email shortly.';
        freeTrialForm.appendChild(successDiv);
      }

      freeTrialForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);

        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.textContent;
        submitButton.textContent = 'Sending...';
        submitButton.disabled = true;

        // Send form data to Cloudflare Worker
        fetch('https://fancy-boat-ea9f.jason-1e6.workers.dev?form_type=free_trial', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          // Show success message
          const successMessage = document.getElementById('form-success');
          successMessage.textContent = data.message || 'Thank you for signing up for the free trial! You will receive your access credentials and 10 free tokens via email shortly.';
          successMessage.classList.remove('hidden');

          // Reset form
          this.reset();

          // Reset button
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Hide success message after 5 seconds
          setTimeout(function() {
            document.getElementById('form-success').classList.add('hidden');
          }, 5000);
        })
        .catch(error => {
          console.error('Error submitting form:', error);

          // Show error message
          const successMessage = document.getElementById('form-success');
          successMessage.textContent = 'There was an error processing your request. Please try again later.';
          successMessage.classList.remove('hidden');
          successMessage.classList.remove('bg-green-100', 'text-green-700');
          successMessage.classList.add('bg-red-100', 'text-red-700');

          // Reset button
          submitButton.textContent = originalButtonText;
          submitButton.disabled = false;

          // Hide error message after 5 seconds
          setTimeout(function() {
            document.getElementById('form-success').classList.add('hidden');
            document.getElementById('form-success').classList.remove('bg-red-100', 'text-red-700');
            document.getElementById('form-success').classList.add('bg-green-100', 'text-green-700');
          }, 5000);
        });
      });
    });
  </script>

  <!-- Add keyframes and animation classes -->
  <style>
    /* Floating geometric elements */
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    /* Pulse halo effect like Contact page */
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(0,191,212,0.7); }
      70% { box-shadow: 0 0 0 10px rgba(0,191,212,0); }
      100% { box-shadow: 0 0 0 0 rgba(0,191,212,0); }
    }

    @keyframes fadeInUp {
      from { opacity: 0; transform: translate3d(0,40px,0); }
      to { opacity: 1; transform: translate3d(0,0,0); }
    }

    @keyframes fadeInLeft {
      from { opacity: 0; transform: translate3d(-30px,0,0); }
      to { opacity: 1; transform: translate3d(0,0,0); }
    }

    @keyframes fadeInRight {
      from { opacity: 0; transform: translate3d(30px,0,0); }
      to { opacity: 1; transform: translate3d(0,0,0); }
    }

    .float-animation {
      animation: float 6s ease-in-out infinite;
    }

    .pulse-animation {
      animation: pulse 2s infinite;
    }

    .animate-title {
      animation: fadeInUp 0.8s ease-out forwards;
    }

    .animate-subtitle {
      opacity: 0;
      animation: fadeInUp 0.8s ease-out 0.2s forwards;
      animation-fill-mode: both;
    }

    .animate-card-left {
      opacity: 0;
      animation: fadeInLeft 0.8s ease-out 0.4s forwards;
      animation-fill-mode: both;
    }

    .animate-card-right {
      opacity: 0;
      animation: fadeInRight 0.8s ease-out 0.4s forwards;
      animation-fill-mode: both;
    }
  </style>

  <!-- Parallax Effect Script -->
  <script>
    // Parallax Effect
    window.addEventListener('scroll', function() {
      const parallaxSections = document.querySelectorAll('.parallax-section');

      parallaxSections.forEach(section => {
        const distance = window.scrollY - section.offsetTop;
        const isInView = window.scrollY + window.innerHeight > section.offsetTop &&
                        window.scrollY < section.offsetTop + section.offsetHeight;

        if (isInView) {
          // Parallax for layers
          const layers = section.querySelectorAll('.parallax-layer');
          layers.forEach(layer => {
            const speed = layer.getAttribute('data-speed') || 0.05;
            layer.style.transform = `translateY(${distance * speed}px)`;
          });

          // Parallax for individual elements
          const elements = section.querySelectorAll('.parallax-element');
          elements.forEach(element => {
            const speed = element.getAttribute('data-speed') || 0.1;
            const translateY = distance * speed;
            element.style.transform = element.style.transform
              ? element.style.transform.replace(/translateY\([^)]+\)/, '') + ` translateY(${translateY}px)`
              : `translateY(${translateY}px)`;
          });
        }
      });
    });
  </script>
</body>
</html>
