/**
 * CaseBuilder Chatbot Widget
 *
 * This script creates a floating chatbot widget that can be included in any page
 * of the CaseBuilder website.
 */

class CaseBuilderChatbot {
  constructor(options = {}) {
    // Default configuration
    this.config = {
      apiUrl: 'https://fancy-boat-ea9f.jason-1e6.workers.dev/chatbot',
      position: 'bottom-right',
      primaryColor: '#00BFD4',
      secondaryColor: '#00E1FF',
      darkBg: '#121212',
      cardBg: '#1E1E1E',
      textColor: '#FFFFFF',
      textSecondary: '#CCCCCC',
      welcomeMessage: 'Hello! I\'m the CaseBuilder AI Assistant. I can help you learn about our AI-powered case analysis tools for personal injury attorneys. How can I assist you today?',
      title: 'CaseBuilder AI Assistant',
      ...options
    };

    // Initialize conversation history
    this.conversationHistory = [];

    // Create and inject the chatbot HTML
    this.createChatbotHTML();

    // Initialize event listeners
    this.initEventListeners();
  }

  createChatbotHTML() {
    // Create the CSS styles
    const styles = document.createElement('style');
    styles.textContent = `
      .cb-chatbot-widget {
        position: fixed;
        z-index: 9999;
        font-family: 'Arial', sans-serif;
      }

      .cb-chatbot-widget.bottom-right {
        bottom: 20px;
        right: 20px;
      }

      .cb-chatbot-widget.bottom-left {
        bottom: 20px;
        left: 20px;
      }

      .cb-chatbot-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: ${this.config.primaryColor};
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
      }

      .cb-chatbot-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        background-color: ${this.config.secondaryColor};
      }

      .cb-chatbot-icon {
        width: 30px;
        height: 30px;
        fill: ${this.config.darkBg};
      }

      .cb-chatbot-container {
        position: absolute;
        bottom: 70px;
        right: 0;
        width: 350px;
        height: 500px;
        background-color: ${this.config.cardBg};
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 191, 212, 0.3);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        opacity: 0;
        transform: translateY(20px) scale(0.9);
        pointer-events: none;
        transition: all 0.3s ease;
      }

      .cb-chatbot-widget.bottom-left .cb-chatbot-container {
        right: auto;
        left: 0;
      }

      .cb-chatbot-container.active {
        opacity: 1;
        transform: translateY(0) scale(1);
        pointer-events: all;
      }

      .cb-chatbot-header {
        background-color: ${this.config.primaryColor};
        color: ${this.config.darkBg};
        padding: 15px 20px;
        font-size: 1.2rem;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .cb-chatbot-close {
        cursor: pointer;
        font-size: 1.5rem;
        line-height: 1;
      }

      .cb-chat-messages {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .cb-message {
        max-width: 80%;
        padding: 12px 15px;
        border-radius: 10px;
        animation: cbFadeIn 0.3s ease-in-out;
      }

      .cb-user-message {
        align-self: flex-end;
        background-color: ${this.config.primaryColor};
        color: ${this.config.darkBg};
        border-bottom-right-radius: 0;
      }

      .cb-bot-message {
        align-self: flex-start;
        background-color: #2A2A2A;
        color: ${this.config.textColor};
        border-bottom-left-radius: 0;
      }

      .cb-chat-input {
        display: flex;
        padding: 15px;
        background-color: #2A2A2A;
        border-top: 1px solid #333;
      }

      .cb-chat-input input {
        flex: 1;
        padding: 12px 15px;
        border: none;
        border-radius: 5px;
        background-color: #3A3A3A;
        color: ${this.config.textColor};
        font-size: 1rem;
      }

      .cb-chat-input input:focus {
        outline: none;
        box-shadow: 0 0 0 2px ${this.config.primaryColor};
      }

      .cb-chat-input button {
        margin-left: 10px;
        padding: 12px 20px;
        background-color: ${this.config.primaryColor};
        color: ${this.config.darkBg};
        border: none;
        border-radius: 5px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .cb-chat-input button:hover {
        background-color: ${this.config.secondaryColor};
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 191, 212, 0.4);
      }

      .cb-chat-input button:disabled {
        background-color: #555;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      .cb-typing-indicator {
        display: flex;
        align-items: center;
        margin-top: 5px;
        margin-left: 15px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .cb-typing-indicator.active {
        opacity: 1;
      }

      .cb-typing-indicator span {
        height: 8px;
        width: 8px;
        background-color: ${this.config.primaryColor};
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
        animation: cbTyping 1s infinite ease-in-out;
      }

      .cb-typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
      }

      .cb-typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes cbTyping {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
      }

      @keyframes cbFadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .cb-message pre {
        background-color: #333;
        padding: 10px;
        border-radius: 5px;
        overflow-x: auto;
        margin: 10px 0;
      }

      .cb-message code {
        font-family: 'Courier New', monospace;
      }

      @media (max-width: 480px) {
        .cb-chatbot-container {
          width: 300px;
          height: 450px;
        }
      }
    `;
    document.head.appendChild(styles);

    // Create the chatbot HTML
    const chatbotHTML = `
      <div class="cb-chatbot-widget ${this.config.position}">
        <div class="cb-chatbot-button">
          <svg class="cb-chatbot-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H5.17L4 17.17V4h16v12z"/>
            <path d="M7 9h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z"/>
          </svg>
        </div>
        <div class="cb-chatbot-container">
          <div class="cb-chatbot-header">
            <span>${this.config.title}</span>
            <span class="cb-chatbot-close">&times;</span>
          </div>
          <div class="cb-chat-messages">
            <div class="cb-message cb-bot-message">
              ${this.config.welcomeMessage}
            </div>
          </div>
          <div class="cb-typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="cb-chat-input">
            <input type="text" placeholder="Type your message here..." autocomplete="off">
            <button>Send</button>
          </div>
        </div>
      </div>
    `;

    // Create a container element and append the chatbot HTML
    const container = document.createElement('div');
    container.innerHTML = chatbotHTML;
    document.body.appendChild(container.firstElementChild);

    // Store references to DOM elements
    this.chatbotButton = document.querySelector('.cb-chatbot-button');
    this.chatbotContainer = document.querySelector('.cb-chatbot-container');
    this.chatbotClose = document.querySelector('.cb-chatbot-close');
    this.chatMessages = document.querySelector('.cb-chat-messages');
    this.userInput = document.querySelector('.cb-chat-input input');
    this.sendButton = document.querySelector('.cb-chat-input button');
    this.typingIndicator = document.querySelector('.cb-typing-indicator');
  }

  initEventListeners() {
    // Toggle chatbot visibility
    this.chatbotButton.addEventListener('click', () => {
      this.chatbotContainer.classList.add('active');
      this.userInput.focus();
    });

    // Close chatbot
    this.chatbotClose.addEventListener('click', () => {
      this.chatbotContainer.classList.remove('active');
    });

    // Send message on button click
    this.sendButton.addEventListener('click', () => {
      this.sendUserMessage();
    });

    // Send message on Enter key
    this.userInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.sendUserMessage();
      }
    });
  }

  sendUserMessage() {
    const message = this.userInput.value.trim();
    if (message) {
      // Add user message to chat
      this.addMessage(message, true);
      this.userInput.value = '';

      // Send message to API
      this.sendMessageToAPI(message);
    }
  }

  addMessage(message, isUser) {
    const messageElement = document.createElement('div');
    messageElement.classList.add('cb-message');
    messageElement.classList.add(isUser ? 'cb-user-message' : 'cb-bot-message');

    // Convert markdown-style code blocks to HTML
    let formattedMessage = message.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    messageElement.innerHTML = formattedMessage;
    this.chatMessages.appendChild(messageElement);

    // Scroll to the bottom of the chat
    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;

    // Add to conversation history
    if (isUser) {
      this.conversationHistory.push({ role: 'user', content: message });
    } else {
      this.conversationHistory.push({ role: 'assistant', content: message });
    }
  }

  async sendMessageToAPI(message) {
    try {
      // Show typing indicator
      this.typingIndicator.classList.add('active');

      // Disable input while waiting for response
      this.userInput.disabled = true;
      this.sendButton.disabled = true;

      // Send request to the chatbot API
      const response = await fetch(this.config.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: message,
          history: this.conversationHistory
        })
      });

      const data = await response.json();

      // Hide typing indicator
      this.typingIndicator.classList.remove('active');

      // Re-enable input
      this.userInput.disabled = false;
      this.sendButton.disabled = false;
      this.userInput.focus();

      if (data.success) {
        // Add bot response to chat
        this.addMessage(data.message, false);
      } else {
        // Handle error
        this.addMessage('Sorry, I encountered an error: ' + (data.message || 'Unknown error'), false);
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Hide typing indicator
      this.typingIndicator.classList.remove('active');

      // Re-enable input
      this.userInput.disabled = false;
      this.sendButton.disabled = false;

      // Add error message to chat
      this.addMessage('Sorry, I encountered an error while processing your request. Please try again later.', false);
    }
  }
}

// Initialize the chatbot when the script is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create global instance
  window.caseBuilderChatbot = new CaseBuilderChatbot();
});
