<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CaseBuilder</title>
    <link rel="icon" href="/favicon.png" type="image/png" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      /* Custom styles for expandable content */
      #medical-summary-preview,
      #damages-breakdown-preview,
      #injury-analysis-preview,
      #demand-letter-preview {
        overflow: hidden;
        transition: max-height 0.5s ease, opacity 0.5s ease;
      }

      /* Hover effect for the preview buttons */
      #preview-medical-summary:hover,
      #preview-damages-breakdown:hover,
      #preview-injury-analysis:hover,
      #preview-demand-letter:hover {
        text-decoration: underline;
      }

      /* Horizontal rule style */
      .preview-divider {
        border: 0;
        height: 1px;
        background-color: #e5e7eb;
        margin: 1rem 0;
      }

      /* Custom color styles */
      .demo-button {
        background-color: #00BFD4 !important;
      }
      .demo-button:hover {
        background-color: #00d0e7 !important; /* Slightly lighter version for hover */
      }

      /* Custom text color */
      .custom-blue-text {
        color: #00BFD4 !important;
      }
      .custom-blue-text:hover {
        color: #00d0e7 !important;
      }

      /* Custom background color */
      .custom-blue-bg {
        background-color: #00BFD4 !important;
      }
      .custom-blue-bg:hover {
        background-color: #00d0e7 !important;
      }

      /* Custom border color */
      .custom-blue-border:hover {
        border-color: #00BFD4 !important;
      }
      /* CTA pulse halo effect */
      @keyframes cta-pulse {
        0% { box-shadow: 0 0 0 0 rgba(0,191,212,0.7); }
        70% { box-shadow: 0 0 0 10px rgba(0,191,212,0); }
        100% { box-shadow: 0 0 0 0 rgba(0,191,212,0); }
      }
      .cta-pulse {
        animation: cta-pulse 2s infinite;
      }
    </style>
  </head>
  <body class="bg-gray-900 text-white">
    <!-- Navbar -->
    <nav id="navbar" class="flex justify-between items-center px-6 py-0 shadow-xl bg-gray-900 text-white border-b border-gray-800 sticky top-0 z-50 transition-transform duration-300">
      <div class="flex items-center">
        <a href="index.html">
          <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-180" />
        </a>
      </div>


      <!-- Desktop menu -->
      <ul class="flex gap-6 text-sm font-medium">
        <li>
          <a
            href="https://app.casebuilder.ai"
            target="_blank"
            class="custom-blue-bg text-white px-6 py-3 rounded-lg text-base font-medium hover:custom-blue-bg shadow-md"
            >Log In</a
          >
        </li>
      </ul>
    </nav>


    <!-- Add JavaScript for mobile menu toggle and navbar scroll behavior -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {

        // Navbar scroll behavior
        const navbar = document.getElementById('navbar');
        let lastScrollTop = 0;

        window.addEventListener('scroll', function() {
          let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

          if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down & past threshold
            navbar.style.transform = 'translateY(-100%)';
          } else {
            // Scrolling up or at the top
            navbar.style.transform = 'translateY(0)';
          }

          lastScrollTop = scrollTop;
        });
      });
    </script>

    <!-- Chatbot Widget Script -->
    <script src="js/chatbot-widget.js"></script>

    <!-- Hero Section -->
    <section class="hero-section bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20 relative overflow-hidden parallax-section">
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(0,191,212,0.1),transparent_60%)]"></div>

      <!-- Geometric Elements -->
      <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.05">
        <!-- Circles -->
        <div class="absolute top-20 left-10 w-64 h-64 rounded-full border-2 border-[#00BFD4]/40 transform rotate-45 parallax-element" data-speed="0.15"></div>
        <div class="absolute top-40 left-20 w-32 h-32 rounded-full border-2 border-[#00BFD4]/30 parallax-element" data-speed="-0.1"></div>

        <!-- Lines -->
        <div class="absolute top-0 right-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="0.08"></div>
        <div class="absolute bottom-0 left-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent parallax-element" data-speed="-0.12"></div>

        <!-- Triangles -->
        <div class="absolute bottom-20 right-20 w-40 h-40 border-t-2 border-l-2 border-[#00BFD4]/30 transform -rotate-45 parallax-element" data-speed="0.2"></div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 parallax-element" data-speed="0.02" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 30px 30px;"></div>
      </div>

      <div class="max-w-5xl mx-auto text-center px-6 relative z-10">
        <p class="text-sm uppercase tracking-wide text-gray-400 mb-3">Built for Personal Injury Law Firms</p>

        <h1 class="text-4xl sm:text-5xl font-extrabold text-white leading-tight">
          AI-Powered Case Analysis.<br />
          Smarter Demands. Faster Settlements.
        </h1>

        <p class="mt-5 text-lg text-gray-300">
          Instantly generate medical summaries, analyze police reports, and draft demand letters — in one click.
        </p>

        <div class="mt-8">
          <a href="https://casebuilder.ai/free-trial" class="demo-button px-6 py-3 text-white rounded-xl transition-all duration-300 transform hover:scale-105 inline-flex items-center hover:shadow-[0_0_20px_rgba(0,225,255,0.5)] relative overflow-hidden group">
            Build Your Case Now
          </a>
          <p class="text-xs text-gray-500 mt-2">No demos. No delays. Just results.</p>
        </div>
      </div>
    </section>

<!-- Features Section -->
<section class="px-6 py-20 bg-gradient-to-b from-gray-900 via-gray-850 to-gray-800 relative overflow-hidden parallax-section">
  <div class="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,rgba(0,191,212,0.15),transparent_70%)]"></div>

  <!-- Geometric Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.04">
    <!-- Hexagons -->
    <div class="absolute top-40 right-20 w-60 h-60 parallax-element" data-speed="-0.12" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); border: 2px solid rgba(0,191,212,0.2);"></div>
    <div class="absolute bottom-60 left-10 w-40 h-40 parallax-element" data-speed="0.18" style="background: linear-gradient(127deg, rgba(0,191,212,0.15), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); transform: rotate(30deg);"></div>

    <!-- Diagonal Lines -->
    <div class="absolute top-20 left-1/4 w-[2px] h-40 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform rotate-45 parallax-element" data-speed="0.1"></div>
    <div class="absolute bottom-40 right-1/3 w-[2px] h-60 bg-gradient-to-b from-transparent via-[#00BFD4]/50 to-transparent transform -rotate-45 parallax-element" data-speed="-0.08"></div>

    <!-- Squares -->
    <div class="absolute top-1/4 right-1/3 w-20 h-20 border-2 border-[#00BFD4]/30 transform rotate-15 parallax-element float-animation" data-speed="0.15"></div>

    <!-- Dots Pattern -->
    <div class="absolute right-0 bottom-0 w-1/2 h-1/2 parallax-element" data-speed="0.03" style="background-image: radial-gradient(circle, rgba(0,191,212,0.15) 2px, transparent 2px); background-size: 20px 20px;"></div>
  </div>

  <div class="max-w-6xl mx-auto relative z-10">
    <div class="text-center mb-16">
      <h2 class="text-3xl font-bold text-white mb-4">Why CaseBuilder?</h2>
      <p class="text-lg text-gray-300 max-w-3xl mx-auto">
        Our platform combines powerful AI technology with intuitive design to help personal injury attorneys build stronger cases in less time.
      </p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-10 max-w-5xl mx-auto">
      <!-- Feature Card: Comprehensive Medical Summaries -->
      <div id="medical-summary-card" class="bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2 border border-gray-700 hover:border-[#00BFD4]/30 group">
        <div class="flex items-center mb-6 group">
          <div class="rounded-full p-3 mr-4 bg-white border border-transparent transition-all duration-300 group-hover:bg-[#00BFD4] group-hover:border-white group-hover:shadow-[0_0_10px_rgba(0,191,212,0.5)]">
            <svg class="h-8 w-8 text-[#00BFD4] transition-all duration-300 group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h3 class="font-semibold text-xl text-white">Comprehensive Medical Summaries</h3>
        </div>
        <p class="text-gray-300 ml-16">Our AI extracts diagnoses, treatments, and ICD codes directly from medical records—no manual input required. It instantly generates structured summaries that surface the most relevant medical facts to support your case strategy.</p>
        <div class="mt-4 ml-16">
          <button id="preview-medical-summary" class="inline-block custom-blue-text font-medium transition-all duration-300 cursor-pointer hover:text-[#00E1FF] hover:shadow-[0_0_10px_rgba(0,191,212,0.4)] px-2 py-1 rounded">Preview a medical summary →</button>
        </div>

        <!-- Expandable content (hidden by default) -->
        <div id="medical-summary-preview" class="hidden mt-6 ml-16 p-4 bg-gray-700 rounded-lg border border-gray-600">
          <h4 class="font-semibold text-lg text-white mb-3 flex items-center">
            <svg class="h-5 w-5 custom-blue-text mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Sample Medical Summary
          </h4>

          <div class="text-sm text-gray-200 space-y-3">
            <p><strong>Patient:</strong> Jane Doe</p>
            <p><strong>Date of Incident:</strong> March 12, 2024</p>

            <div>
              <p class="font-medium">Primary Diagnoses:</p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Cervical strain (ICD-10: S13.4)</li>
                <li>Right shoulder sprain (ICD-10: S43.4)</li>
                <li>Headache, post-traumatic (ICD-10: R51.9)</li>
              </ul>
            </div>

            <div>
              <p class="font-medium">Treatment Timeline:</p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>03/13/2024 – Initial ER visit: CT scan, pain management</li>
                <li>03/15/2024 – Chiropractic consultation: Range of motion limited</li>
                <li>03/21/2024 – Physical therapy initiated (8 sessions)</li>
              </ul>
            </div>

            <div>
              <p class="font-medium flex items-center">
                <svg class="h-4 w-4 custom-blue-text mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                AI Highlights:
              </p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Patient reported 7/10 pain in cervical area</li>
                <li>Limited mobility noted during second PT visit</li>
                <li>Consistent symptoms support injury-related impairment</li>
              </ul>
            </div>

            <div class="mt-4 flex items-center text-green-600 font-medium">
              <svg class="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Ready to copy into demand draft
            </div>
          </div>
        </div>
      </div>

      <!-- Feature Card: Smart Damages Calculation -->
      <div id="damages-calculation-card" class="bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2 border border-gray-700 hover:border-[#00BFD4]/30 group">
        <div class="flex items-center mb-6 group">
          <div class="rounded-full p-3 mr-4 bg-white border border-transparent transition-all duration-300 group-hover:bg-[#00BFD4] group-hover:border-white group-hover:shadow-[0_0_10px_rgba(0,191,212,0.5)]">
            <svg class="h-8 w-8 text-[#00BFD4] transition-all duration-300 group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="font-semibold text-xl text-white">Smart Damages Calculation</h3>
        </div>
        <p class="text-gray-300 ml-16">Our AI turns treatment data into bigger settlements—quantifying pain, suffering, and medical costs with precision. No spreadsheets, no guesswork—just stronger negotiations and faster resolutions.</p>
        <div class="mt-4 ml-16">
          <button id="preview-damages-breakdown" class="inline-block custom-blue-text font-medium transition-all duration-300 cursor-pointer hover:text-[#00E1FF] hover:shadow-[0_0_10px_rgba(0,191,212,0.4)] px-2 py-1 rounded">Preview a damages breakdown →</button>
        </div>

        <!-- Expandable content (hidden by default) -->
        <div id="damages-breakdown-preview" class="hidden mt-6 ml-16 p-4 bg-gray-700 rounded-lg border border-gray-600">
          <h4 class="font-semibold text-lg text-white mb-3 flex items-center">
            <svg class="h-5 w-5 custom-blue-text mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Sample Damages Breakdown
          </h4>

          <div class="text-sm text-gray-200 space-y-3">
            <p><strong>Client:</strong> Jane Doe</p>
            <p><strong>Date of Incident:</strong> March 12, 2024</p>

            <div>
              <p class="font-medium">Documented Medical Expenses:</p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Emergency Room Visit: $2,280.00</li>
                <li>Chiropractic Care: $6,440.00</li>
                <li>Physical Therapy (8 sessions): $2,400.00</li>
                <li>MRI studies: $4,800.00</li>
                <li>Prescription Medications: $120.00</li>
              </ul>
              <p class="font-medium mt-2">Total Medicals: $16,040.00</p>
            </div>

            <div>
              <p class="font-medium">Pain & Suffering Estimate:</p>
              <p class="text-xs text-gray-500 mt-1">Based on injury severity, treatment duration, and documented pain levels</p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Moderate cervical and shoulder strain</li>
                <li>AI-estimated multiplier: 2.0</li>
                <li>Total P&S: $32,080.00</li>
              </ul>
            </div>

            <div>
              <p class="font-medium flex items-center">
                <svg class="h-4 w-4 custom-blue-text mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                AI Highlights:
              </p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Prolonged PT supports ongoing pain</li>
                <li>Headache diagnosis justifies added general damages</li>
                <li>Consistent 7/10 pain ratings support multiplier value</li>
              </ul>
            </div>

            <div class="mt-4 flex items-center text-green-600 font-medium">
              <svg class="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Ready to include in demand letter draft
            </div>
          </div>
        </div>
      </div>

      <!-- Feature Card: Visual Evidence Integration -->
      <div id="visual-evidence-card" class="bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2 border border-gray-700 hover:border-[#00BFD4]/30 group">
        <div class="flex items-center mb-6 group">
          <div class="rounded-full p-3 mr-4 bg-white border border-transparent transition-all duration-300 group-hover:bg-[#00BFD4] group-hover:border-white group-hover:shadow-[0_0_10px_rgba(0,191,212,0.5)]">
            <svg class="h-8 w-8 text-[#00BFD4] transition-all duration-300 group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="font-semibold text-xl text-white">Visual Evidence Integration</h3>
        </div>
        <p class="text-gray-300 ml-16">Upload client injury photos and let the AI do the rest. CaseBuilder analyzes visible trauma and cross-references it with police or medical reports to generate a powerful legal summary—instantly aligned with the narrative of your case.</p>
        <div class="mt-4 ml-16">
          <button id="preview-injury-analysis" class="inline-block custom-blue-text font-medium transition-all duration-300 cursor-pointer hover:text-[#00E1FF] hover:shadow-[0_0_10px_rgba(0,191,212,0.4)] px-2 py-1 rounded">Preview injury analysis →</button>
        </div>

        <!-- Expandable content (hidden by default) -->
        <div id="injury-analysis-preview" class="hidden mt-6 ml-16 p-4 bg-gray-700 rounded-lg border border-gray-600">
          <h4 class="font-semibold text-lg text-white mb-3 flex items-center">
            <svg class="h-5 w-5 custom-blue-text mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Sample Injury Analysis Summary
          </h4>

          <div class="text-sm text-gray-200 space-y-3">
          <p><strong>Client:</strong> Jane Doe</p>
          <p><strong>Document Context:</strong></p>
          <ul class="list-disc pl-5 space-y-1 mt-1">
            <li>Police Report</li>
            <li>Medical Records</li>
          </ul>
          <p><strong>Image:</strong> janedoepic.png</p>
          <hr class="preview-divider" />

          <div>
            <p class="font-medium">Excerpt – Visual Narrative:</p>
            <p class="mt-2 italic text-gray-300 bg-gray-600 p-3 rounded border border-gray-500">
              “The image shows a laceration on Ms. Doe's forehead with visible swelling and redness. These injuries, combined with documented treatment and the police report, confirm direct trauma from the impact. The photo substantiates the mechanism of injury and reinforces claims of physical pain, emotional distress, and potential long-term impairment.”
            </p>
          </div>

          <div class="mt-4 flex items-center text-green-600 font-medium">
            <svg class="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Image analysis integrated into demand draft
          </div>
          </div>
        </div>
      </div>

      <!-- Feature Card: AI-Powered Demand Letters -->
      <div id="demand-letter-card" class="bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-[0_0_15px_rgba(0,191,212,0.15)] hover:shadow-[0_0_20px_rgba(0,191,212,0.3)] transition-all duration-300 transform hover:-translate-y-2 border border-gray-700 hover:border-[#00BFD4]/30 group">
        <div class="flex items-center mb-6 group">
          <div class="rounded-full p-3 mr-4 bg-white border border-transparent transition-all duration-300 group-hover:bg-[#00BFD4] group-hover:border-white group-hover:shadow-[0_0_10px_rgba(0,191,212,0.5)]">
            <svg class="h-8 w-8 text-[#00BFD4] transition-all duration-300 group-hover:text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="font-semibold text-xl text-white">AI-Powered Demand Letters</h3>
        </div>
        <p class="text-gray-300 ml-16">Draft persuasive demand letters in minutes. CaseBuilder analyzes medical records, police reports, and visual evidence, then crafts fully structured letters tailored to your firm's strategy—no templates, no manual writing.</p>
        <div class="mt-4 ml-16">
          <button id="preview-demand-letter" class="inline-block custom-blue-text font-medium transition-all duration-300 cursor-pointer hover:text-[#00E1FF] hover:shadow-[0_0_10px_rgba(0,191,212,0.4)] px-2 py-1 rounded">Preview a demand letter →</button>
        </div>

        <!-- Expandable content (hidden by default) -->
        <div id="demand-letter-preview" class="hidden mt-6 ml-16 p-4 bg-gray-700 rounded-lg border border-gray-600">
          <h4 class="font-semibold text-lg text-white mb-3 flex items-center">
            <svg class="h-5 w-5 custom-blue-text mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Sample Demand Letter – Jane Doe
          </h4>

          <div class="text-sm text-gray-200 space-y-3">
            <div>
              <p class="font-medium">Customizable Settings:</p>
              <ul class="list-disc pl-5 space-y-1 mt-1">
                <li>Length: Standard</li>
                <li>Tone: Firm</li>
                <li>Emphasis: Liability & Quality of Life Impact</li>
              </ul>
            </div>

            <div>
              <p class="font-medium">Integrated Sources:</p>
              <div class="flex flex-wrap gap-2 mt-1">
                <span class="flex items-center text-green-600">
                  <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Medical Records
                </span>
                <span class="flex items-center text-green-600">
                  <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Police Report
                </span>
                <span class="flex items-center text-green-600">
                  <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Injury Photo
                </span>
              </div>
            </div>

            <hr class="preview-divider" />

            <div>
              <p class="font-medium">Excerpt – Demand Narrative:</p>
              <p class="mt-2 italic text-gray-300 bg-gray-600 p-3 rounded border border-gray-500">
                "The breach of duty by your insured is clear, as they failed to adhere to traffic laws designed to prevent accidents. Specifically, the violation of CVC § 21802(a) directly correlates with the damages suffered by Ms. Doe. Despite any potential claims to shared liability, we assert that your insured is wholly responsible for this collision and its consequences."
              </p>
            </div>

            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
              <div>
                <p class="font-medium">General Damages Estimated:</p>
                <p class="text-blue-600 font-bold">$145,000 – $176,000</p>
              </div>
              <div>
                <p class="font-medium">Policy Limit Demand:</p>
                <p class="text-green-600 font-bold flex items-center">
                  <svg class="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Activated
                </p>
              </div>
            </div>

            <div class="mt-4 flex items-center text-green-600 font-medium">
              <svg class="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              Letter ready for export and delivery
            </div>

            <hr class="preview-divider" />
          </div>
        </div>
      </div>
    </div>


  </div>
</section>

<!-- Key Features Section -->
<section id="key-features" class="py-20 bg-gradient-to-t from-gray-900 via-gray-850 to-gray-800 relative overflow-hidden parallax-section">
  <div class="absolute inset-0 bg-[radial-gradient(circle_at_70%_70%,rgba(0,191,212,0.1),transparent_60%)]"></div>

  <!-- Geometric Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.05">
    <!-- Triangles -->
    <div class="absolute top-20 left-20 w-0 h-0 border-l-[60px] border-r-[60px] border-b-[100px] border-l-transparent border-r-transparent border-b-[#00BFD4]/30 parallax-element" data-speed="0.1"></div>
    <div class="absolute bottom-40 right-20 w-0 h-0 border-l-[40px] border-r-[40px] border-t-[70px] border-l-transparent border-r-transparent border-t-[#00BFD4]/30 parallax-element" data-speed="-0.15"></div>

    <!-- Curved Lines -->
    <div class="absolute top-1/3 right-1/4 w-40 h-40 rounded-full border-t-2 border-r-2 border-[#00BFD4]/30 transform rotate-45 parallax-element" data-speed="-0.08"></div>
    <div class="absolute bottom-1/4 left-1/3 w-60 h-60 rounded-full border-b-2 border-l-2 border-[#00BFD4]/30 transform -rotate-12 parallax-element" data-speed="0.12"></div>

    <!-- Diamond -->
    <div class="absolute top-1/2 left-20 w-20 h-20 border-2 border-[#00BFD4]/40 transform rotate-45 parallax-element" data-speed="0.2"></div>

    <!-- Grid Pattern -->
    <div class="absolute left-0 top-0 w-1/2 h-1/2 parallax-element" data-speed="0.03" style="background-image: linear-gradient(rgba(0,191,212,0.15) 1px, transparent 1px), linear-gradient(90deg, rgba(0,191,212,0.15) 1px, transparent 1px); background-size: 20px 20px;"></div>
  </div>

  <div class="max-w-6xl mx-auto px-6 relative z-10">
    <div class="text-center mb-16">
      <h2 class="text-3xl font-bold text-white mb-4">Advanced Tools That Work Like a Legal Assistant (But Faster)</h2>
      <p class="text-lg text-gray-300 max-w-3xl mx-auto">
        CaseBuilder transforms how personal injury attorneys build cases with advanced AI technology.
        Our platform automatically processes documents, analyzes evidence, and generates compelling
        demand letters—turning hours of manual work into minutes of automated precision.
      </p>
    </div>

    <div class="grid md:grid-cols-2 gap-12">
      <!-- Left Column -->
      <div class="space-y-10">
        <!-- Feature 1: Automated PDF Intake -->
        <div class="flex">
          <div class="flex-shrink-0 mt-1">
            <div class="flex items-center justify-center h-12 w-12 rounded-md custom-blue-bg text-white transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.6)] hover:scale-110">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-semibold text-white">Automated PDF Intake</h3>
            <p class="mt-2 text-gray-300">Upload any case document and our AI instantly recognizes and processes it. No manual data entry required—just drag, drop, and let CaseBuilder extract all relevant information automatically.</p>
          </div>
        </div>

        <!-- Feature 2: Police Report Analysis -->
        <div class="flex">
          <div class="flex-shrink-0 mt-1">
            <div class="flex items-center justify-center h-12 w-12 rounded-md custom-blue-bg text-white transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.6)] hover:scale-110">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-semibold text-white">Police Report Analysis</h3>
            <p class="mt-2 text-gray-300">Transform complex police reports into clear liability assessments. Our AI identifies fault indicators, extracts witness statements, and builds compelling accident narratives that strengthen your case.</p>
          </div>
        </div>

        <!-- Feature 3: Medical Report Analysis -->
        <div class="flex">
          <div class="flex-shrink-0 mt-1">
            <div class="flex items-center justify-center h-12 w-12 rounded-md custom-blue-bg text-white transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.6)] hover:scale-110">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-semibold text-white">Medical Report Analysis</h3>
            <p class="mt-2 text-gray-300">Instantly extract diagnoses, treatments, and ICD codes from medical records. Our AI creates comprehensive injury summaries and treatment timelines that clearly demonstrate the impact on your client.</p>
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="space-y-10 flex items-center">
        <!-- Feature 4: Personalization Tools -->
        <div class="flex">
          <div class="flex-shrink-0 mt-1">
            <div class="flex items-center justify-center h-12 w-12 rounded-md custom-blue-bg text-white transition-all duration-300 hover:shadow-[0_0_15px_rgba(0,191,212,0.6)] hover:scale-110">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-semibold text-white">Personalization Tools</h3>
            <p class="mt-2 text-gray-300">Tailor every document to match your firm’s unique voice and strategy. Effortlessly adjust tone, level of detail, and formatting style using intuitive controls—ensuring consistency, clarity, and alignment with your case approach. </p>

            <ul class="mt-4 space-y-2">
              <li class="flex items-start">
                <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span class="text-gray-300">Adjust tone, focus, and letter length to match your case strategy</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span class="text-gray-300">Fine-tune how much emphasis is placed on liability, damages, or losses</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span class="text-gray-300">Select preferred styles for medical summaries and police report analysis</span>
              </li>
              <li class="flex items-start">
                <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span class="text-gray-300">Use the built-in Assistant to navigate settings and optimize results</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Workflow Showcase -->
    <div class="mt-16 bg-gradient-to-br from-gray-700 to-gray-800 p-8 rounded-xl shadow-lg transform transition-all duration-300 hover:shadow-xl border border-gray-600">
      <h3 class="text-2xl font-bold text-white mb-6 text-center">How CaseBuilder Works</h3>

      <div class="grid md:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-850 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4] transition-all duration-300 transform hover:-translate-y-1 hover:shadow-[0_0_15px_rgba(0,191,212,0.15)]">
          <div class="flex items-center mb-4">
            <div class="flex items-center justify-center h-10 w-10 rounded-full custom-blue-bg text-white font-bold text-lg mr-3">1</div>
            <h4 class="text-xl font-semibold text-white">Upload Case Evidence</h4>
          </div>
          <ul class="space-y-3 text-gray-300">
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Drag & drop police reports, medical records, and images</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Automatic document classification</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Local, secure browser-side processing</span>
            </li>
          </ul>
        </div>

        <!-- Step 2 -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-850 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4] transition-all duration-300 transform hover:-translate-y-1 hover:shadow-[0_0_15px_rgba(0,191,212,0.15)]">
          <div class="flex items-center mb-4">
            <div class="flex items-center justify-center h-10 w-10 rounded-full custom-blue-bg text-white font-bold text-lg mr-3">2</div>
            <h4 class="text-xl font-semibold text-white">AI-Powered Analysis</h4>
          </div>
          <ul class="space-y-3 text-gray-300">
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Analyze liability, damages, and injury evidence</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Detect ICD codes, treatment timelines, and visual trauma</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Surface key facts that matter in negotiations</span>
            </li>
          </ul>
        </div>

        <!-- Step 3 -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-850 p-6 rounded-lg border border-gray-700 hover:border-[#00BFD4] transition-all duration-300 transform hover:-translate-y-1 hover:shadow-[0_0_15px_rgba(0,191,212,0.15)]">
          <div class="flex items-center mb-4">
            <div class="flex items-center justify-center h-10 w-10 rounded-full custom-blue-bg text-white font-bold text-lg mr-3">3</div>
            <h4 class="text-xl font-semibold text-white">Generate Demand Letters</h4>
          </div>
          <ul class="space-y-3 text-gray-300">
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Customize tone, focus, and letter length</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Auto-generate narrative, damages, and legal arguments</span>
            </li>
            <li class="flex items-start">
              <svg class="h-5 w-5 custom-blue-text mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <span>Export in Word format — ready to review or send</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- CTA Button -->
      <div class="mt-10 text-center">
        <a href="https://casebuilder.ai/free-trial" class="inline-flex items-center px-6 py-3 custom-blue-bg text-white rounded-lg hover:custom-blue-bg transition-colors duration-300 transform hover:scale-105 cta-pulse">
          <span class="mr-2">Get 10 Free Tokens</span>
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </a>
        <p class="text-xs text-gray-500 mt-2">No credit card. No setup. Instant output.</p>
      </div>
    </div>
  </div>
</section>

<!-- Client Privacy Section -->
<section id="compliance" class="bg-gradient-to-br from-gray-900 via-gray-850 to-gray-800 py-16 relative overflow-hidden parallax-section">
  <div class="absolute inset-0 bg-[radial-gradient(circle_at_10%_50%,rgba(0,191,212,0.08),transparent_70%)]"></div>

  <!-- Geometric Elements -->
  <div class="absolute inset-0 overflow-hidden opacity-40 pointer-events-none parallax-layer" data-speed="0.06">
    <!-- Shield Shapes -->
    <div class="absolute top-20 right-40 w-40 h-50 parallax-element" data-speed="-0.1" style="background: linear-gradient(217deg, rgba(0,191,212,0.2), transparent 70%); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); transform: scaleY(1.2); border: 2px solid rgba(0,191,212,0.2);"></div>

    <!-- Lock Circles -->
    <div class="absolute bottom-20 left-20 w-30 h-30 rounded-full border-2 border-[#00BFD4]/30 parallax-element float-animation" data-speed="0.15"></div>
    <div class="absolute bottom-30 left-30 w-10 h-20 border-t-2 border-l-2 border-r-2 border-[#00BFD4]/30 rounded-t-full parallax-element" data-speed="0.15"></div>

    <!-- Data Flow Lines -->
    <div class="absolute top-1/3 left-0 right-0 h-[2px] parallax-element" data-speed="0.05" style="background: repeating-linear-gradient(90deg, rgba(0,191,212,0.3), rgba(0,191,212,0.3) 10px, transparent 10px, transparent 20px);"></div>
    <div class="absolute bottom-1/4 left-0 right-0 h-[2px] parallax-element" data-speed="-0.05" style="background: repeating-linear-gradient(90deg, rgba(0,191,212,0.3), rgba(0,191,212,0.3) 10px, transparent 10px, transparent 20px);"></div>

    <!-- Binary Pattern -->
    <div class="absolute right-20 top-20 opacity-30 parallax-element" data-speed="-0.2" style="font-family: monospace; font-size: 14px; color: #00BFD4; font-weight: bold;">
      <div>10110101</div>
      <div>01001010</div>
      <div>11010010</div>
      <div>00101101</div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <h2 class="text-3xl font-bold mb-8 text-center text-white">Client Privacy. Guaranteed.</h2>
    <div class="max-w-4xl mx-auto bg-gradient-to-br from-gray-800 to-gray-850 p-8 rounded-xl shadow-lg border border-gray-700 hover:border-[#00BFD4]/30 hover:shadow-xl transition-all duration-300">
      <p class="text-lg mb-6 text-gray-300">
        We take your client’s privacy seriously. CaseBuilder is built to keep sensitive information safe — nothing leaves your device, and nothing is ever saved.
      </p>

      <p class="text-gray-300 font-semibold mb-6">At a glance:</p>

      <ul class="space-y-4 mb-8">
        <li class="flex items-start bg-gradient-to-br from-gray-700 to-gray-750 p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 border border-gray-600 hover:border-[#00BFD4]/20">
          <div class="flex-shrink-0 bg-blue-100 rounded-full p-2 mr-4">
            <svg class="h-6 w-6 custom-blue-text" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <span class="text-gray-300">All files are processed instantly in your browser — not sent anywhere, not stored.</span>
        </li>
        <li class="flex items-start bg-gradient-to-br from-gray-700 to-gray-750 p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 border border-gray-600 hover:border-[#00BFD4]/20">
          <div class="flex-shrink-0 bg-blue-100 rounded-full p-2 mr-4">
            <svg class="h-6 w-6 custom-blue-text" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <span class="text-gray-300">Data is used only for the duration of the task and disappears the moment you’re done.</span>
        </li>
        <li class="flex items-start bg-gradient-to-br from-gray-700 to-gray-750 p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 border border-gray-600 hover:border-[#00BFD4]/20">
          <div class="flex-shrink-0 bg-blue-100 rounded-full p-2 mr-4">
            <svg class="h-6 w-6 custom-blue-text" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <span class="text-gray-300">No backups, no logs, no lingering data. Ever.</span>
        </li>
      </ul>

      <div class="text-center bg-gradient-to-br from-gray-700 to-gray-750 p-4 rounded-lg shadow-sm border border-gray-600 hover:border-[#00BFD4]/20 transition-all duration-300">
        <p class="text-gray-300">
          No documents are ever sent to our servers or shared with third parties. You remain in full control at all times.
        </p>
        <a href="security.html" class="inline-block mt-2 custom-blue-text hover:underline hover:text-[#00E1FF] transition-colors duration-300 font-medium">Read our Data Security Policy →</a>
      </div>
    </div>
  </div>
</section>



<!-- Footer -->
<footer class="bg-gray-900 text-white py-10 mt-20">
  <div class="max-w-7xl mx-auto px-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">

    <!-- Brand -->
    <div>
      <div class="mb-3">
        <img src="images/logo.svg" alt="CaseBuilder Logo" class="h-48 w-auto mb-2 filter brightness-150" />
      </div>
    </div>

    <!-- Navigation -->
    <div>
      <h3 class="text-lg font-semibold mb-2">Navigation</h3>
      <ul class="text-sm text-gray-400 space-y-1">
        <li><a href="index.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Home</a></li>
        <li><a href="pricing.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Pricing</a></li>
        <li><a href="faq.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">FAQ</a></li>
        <li><a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact</a></li>
      </ul>
    </div>

    <!-- Legal -->
    <div>
      <h3 class="text-lg font-semibold mb-2">Legal</h3>
      <ul class="text-sm text-gray-400 space-y-1">
        <li><a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a></li>
        <li><a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a></li>
        <li><a href="security.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Data Security Policy</a></li>
      </ul>
    </div>

    <!-- Contact -->
    <div>
      <h3 class="text-lg font-semibold mb-2">Contact</h3>
      <p class="text-sm text-gray-400 mb-1">131 Continental Dr Suite 305,</p>
      <p class="text-sm text-gray-400 mb-3">Newark, DE 19713</p>
      <p class="text-sm text-gray-400 mb-1">Phone: <a href="tel:+18667513005" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">+****************</a></p>
      <p class="text-sm text-gray-400 mb-3">Email: <a href="mailto:<EMAIL>" class="custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded"><EMAIL></a></p>
      <div class="flex space-x-4">
        <a href="https://www.facebook.com/people/CaseBuilderai/61560221212659" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
          </svg>
        </a>
        <a href="https://www.instagram.com/casebuilderai/" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/>
          </svg>
        </a>
        <a href="https://www.youtube.com/channel/UCpL3DBmoSk5rpDUBnmJccqQ" target="_blank" class="text-gray-400 hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_10px_rgba(0,191,212,0.6)] p-1 rounded-full">
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
          </svg>
        </a>
      </div>
    </div>

  </div>

  <!-- Bottom -->
  <div class="border-t border-gray-800 mt-10 pt-6 text-center text-gray-500 text-sm">
    <p>&copy; 2024 CaseBuilderAI, LLC — All rights reserved.</p>
    <div class="mt-2">
      <a href="terms.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Terms of Use</a> •
      <a href="privacy.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Privacy Policy</a> •
      <a href="contact.html" class="hover:custom-blue-text hover:text-[#00E1FF] transition-all duration-300 hover:shadow-[0_0_8px_rgba(0,191,212,0.5)] px-1 rounded">Contact Us</a>
    </div>
  </div>
</footer>

<!-- Animation Script for Features -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Animate hero section elements on page load
    const heroTitle = document.querySelector('.hero-section h1');
    const heroSubtitle = document.querySelector('.hero-section p.text-lg');
    const heroButton = document.querySelector('.demo-button');

    if (heroTitle) {
      heroTitle.style.opacity = '0';
      heroTitle.style.transform = 'translateY(30px)';

      setTimeout(() => {
        heroTitle.style.transition = 'opacity 1s ease, transform 1s ease';
        heroTitle.style.opacity = '1';
        heroTitle.style.transform = 'translateY(0)';
      }, 300);
    }

    if (heroSubtitle) {
      heroSubtitle.style.opacity = '0';
      heroSubtitle.style.transform = 'translateY(30px)';

      setTimeout(() => {
        heroSubtitle.style.transition = 'opacity 1s ease, transform 1s ease';
        heroSubtitle.style.opacity = '1';
        heroSubtitle.style.transform = 'translateY(0)';
      }, 600);
    }

    if (heroButton) {
      heroButton.style.opacity = '0';
      heroButton.style.transform = 'translateY(30px)';

      setTimeout(() => {
        heroButton.style.transition = 'opacity 1s ease, transform 1s ease';
        heroButton.style.opacity = '1';
        heroButton.style.transform = 'translateY(0)';

        // Add glow effect after button appears
        setTimeout(() => {
          heroButton.classList.add('glow-effect');
        }, 1000);
      }, 900);
    }
    // Animate features on scroll
    const animateOnScroll = function() {
      const keyFeatures = document.querySelectorAll('#key-features .flex');
      const workflowCards = document.querySelectorAll('#key-features .bg-gradient-to-br');
      const complianceItems = document.querySelectorAll('#compliance li');
      const whyFeatureCards = document.querySelectorAll('.bg-gradient-to-b .group');

      const isInViewport = function(element) {
        const rect = element.getBoundingClientRect();
        return (
          rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.85 &&
          rect.bottom >= 0
        );
      };

      // Animate key feature items
      keyFeatures.forEach(function(feature, index) {
        if (isInViewport(feature) && !feature.classList.contains('animated')) {
          setTimeout(function() {
            feature.classList.add('animated');
            feature.style.animation = 'fadeInUp 0.8s ease forwards';

            // Add glow effect to the icon
            const icon = feature.querySelector('.custom-blue-bg');
            if (icon) {
              setTimeout(() => {
                icon.classList.add('glow-effect');
              }, 300);
            }
          }, index * 200);
        }
      });

      // Animate workflow cards
      workflowCards.forEach(function(card, index) {
        if (isInViewport(card) && !card.classList.contains('animated')) {
          setTimeout(function() {
            card.classList.add('animated');
            card.style.animation = 'flipInX 1s ease forwards';

            // Add staggered animation to list items inside the card
            const listItems = card.querySelectorAll('li');
            listItems.forEach((item, i) => {
              setTimeout(() => {
                item.style.animation = 'fadeInRight 0.5s ease forwards';
                item.style.opacity = '0';
                item.style.animationDelay = `${i * 0.15}s`;
              }, 500);
            });
          }, 300 + index * 250);
        }
      });

      // Animate compliance items
      complianceItems.forEach(function(item, index) {
        if (isInViewport(item) && !item.classList.contains('animated')) {
          setTimeout(function() {
            item.classList.add('animated');
            item.style.animation = 'fadeInLeft 0.7s ease forwards';

            // Add glow effect to the icon
            const icon = item.querySelector('.rounded-full');
            if (icon) {
              setTimeout(() => {
                icon.classList.add('glow-effect');
              }, 400);
            }
          }, 200 + index * 250);
        }
      });

      // Animate Why CaseBuilder feature cards
      whyFeatureCards.forEach(function(card, index) {
        if (isInViewport(card) && !card.classList.contains('animated')) {
          setTimeout(function() {
            card.classList.add('animated');
            card.style.animation = 'fadeInUp 0.8s ease forwards';

            // Add staggered animation to the content inside the card
            const cardContent = card.querySelector('p');
            const previewButton = card.querySelector('button');

            if (cardContent) {
              setTimeout(() => {
                cardContent.style.animation = 'fadeInUp 0.6s ease forwards';
                cardContent.style.opacity = '0';
              }, 400);
            }

            if (previewButton) {
              setTimeout(() => {
                previewButton.style.animation = 'fadeInUp 0.6s ease forwards';
                previewButton.style.opacity = '0';

                // Add subtle glow to the button after it appears
                // Use a class instead of directly setting the animation property
                setTimeout(() => {
                  previewButton.classList.add('glow-effect');
                }, 700);
              }, 600);
            }
          }, 200 + index * 200);
        }
      });
    };

    // Set initial styles
    const keyFeatures = document.querySelectorAll('#key-features .flex');
    keyFeatures.forEach(function(feature) {
      feature.style.opacity = '0';
      // Initial styles will be overridden by the animation
    });

    const workflowCards = document.querySelectorAll('#key-features .bg-gradient-to-br');
    workflowCards.forEach(function(card) {
      card.style.opacity = '0';
      // Initial styles will be overridden by the animation

      // Set initial opacity for list items to be animated later
      const listItems = card.querySelectorAll('li');
      listItems.forEach(item => {
        item.style.opacity = '0';
      });
    });

    const complianceItems = document.querySelectorAll('#compliance li');
    complianceItems.forEach(function(item) {
      item.style.opacity = '0';
      // Initial styles will be overridden by the animation
    });

    // Set initial styles for Why CaseBuilder cards
    const whyFeatureCards = document.querySelectorAll('.bg-gradient-to-b .group');
    whyFeatureCards.forEach(function(card) {
      card.style.opacity = '0';
      // Initial styles will be overridden by the animation

      // Set initial opacity for content to be animated later
      const cardContent = card.querySelector('p');
      const previewButton = card.querySelector('button');

      if (cardContent) {
        cardContent.style.opacity = '0';
      }

      if (previewButton) {
        previewButton.style.opacity = '0';
      }
    });

    // Run on load and scroll
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Run once on page load

    // Add hover effects to feature items
    const featureItems = document.querySelectorAll('#key-features .flex');
    featureItems.forEach(function(item) {
      item.addEventListener('mouseenter', function() {
        const icon = this.querySelector('.flex-shrink-0 .custom-blue-bg');
        if (icon) {
          icon.classList.add('scale-110');
          icon.style.transition = 'transform 0.3s ease';
        }
      });

      item.addEventListener('mouseleave', function() {
        const icon = this.querySelector('.flex-shrink-0 .custom-blue-bg');
        if (icon) {
          icon.classList.remove('scale-110');
        }
      });
    });

    // Add hover effects to Why CaseBuilder cards
    whyFeatureCards.forEach(function(card) {
      card.addEventListener('mouseenter', function() {
        const iconBg = this.querySelector('.bg-blue-100');
        const iconSvg = this.querySelector('.custom-blue-text');
        if (iconBg && iconSvg) {
          iconBg.classList.add('custom-blue-bg');
          iconBg.classList.remove('bg-blue-100');
          iconSvg.classList.add('text-white');
          iconSvg.classList.remove('custom-blue-text');
        }
      });

      card.addEventListener('mouseleave', function() {
        const iconBg = this.querySelector('.custom-blue-bg');
        const iconSvg = this.querySelector('.text-white');
        if (iconBg && iconSvg) {
          iconBg.classList.remove('custom-blue-bg');
          iconBg.classList.add('bg-blue-100');
          iconSvg.classList.remove('text-white');
          iconSvg.classList.add('custom-blue-text');
        }
      });
    });

    // Add pulse animation to Why CaseBuilder icons
    const whyFeatureIcons = document.querySelectorAll('.bg-gradient-to-b .rounded-full');
    whyFeatureIcons.forEach(function(icon) {
      // Add subtle pulse animation
      icon.style.animation = 'pulse 2s infinite';

      // Add keyframes and styles for animations
      if (!document.querySelector('#animations-keyframes')) {
        const style = document.createElement('style');
        style.id = 'animations-keyframes';
        style.textContent = `
          /* Class for persistent glow effect */
          .glow-effect {
            animation: glowPulse 3s infinite;
          }

          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translate3d(0, 40px, 0);
            }
            to {
              opacity: 1;
              transform: translate3d(0, 0, 0);
            }
          }

          @keyframes fadeInLeft {
            from {
              opacity: 0;
              transform: translate3d(-40px, 0, 0);
            }
            to {
              opacity: 1;
              transform: translate3d(0, 0, 0);
            }
          }

          @keyframes fadeInRight {
            from {
              opacity: 0;
              transform: translate3d(40px, 0, 0);
            }
            to {
              opacity: 1;
              transform: translate3d(0, 0, 0);
            }
          }

          @keyframes zoomIn {
            from {
              opacity: 0;
              transform: scale3d(0.8, 0.8, 0.8);
            }
            50% {
              opacity: 1;
            }
            to {
              transform: scale3d(1, 1, 1);
            }
          }

          @keyframes flipInX {
            from {
              transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
              animation-timing-function: ease-in;
              opacity: 0;
            }
            40% {
              transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
              animation-timing-function: ease-in;
            }
            60% {
              transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
              opacity: 1;
            }
            80% {
              transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
            }
            to {
              transform: perspective(400px);
              opacity: 1;
            }
          }

          @keyframes bounceIn {
            from, 20%, 40%, 60%, 80%, to {
              animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
            }
            0% {
              opacity: 0;
              transform: scale3d(0.3, 0.3, 0.3);
            }
            20% {
              transform: scale3d(1.1, 1.1, 1.1);
            }
            40% {
              transform: scale3d(0.9, 0.9, 0.9);
            }
            60% {
              opacity: 1;
              transform: scale3d(1.03, 1.03, 1.03);
            }
            80% {
              transform: scale3d(0.97, 0.97, 0.97);
            }
            to {
              opacity: 1;
              transform: scale3d(1, 1, 1);
            }
          }

          @keyframes glowPulse {
            0% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(0, 191, 212, 0); }
            100% { box-shadow: 0 0 0 0 rgba(0, 191, 212, 0); }
          }
        `;
        document.head.appendChild(style);
      }
    });

    // Function to handle expandable preview content
    function setupExpandablePreview(buttonId, contentId, cardId, buttonText) {
      const previewButton = document.getElementById(buttonId);
      const previewContent = document.getElementById(contentId);
      const card = document.getElementById(cardId);

      if (previewButton && previewContent) {
        previewButton.addEventListener('click', function() {
          // Toggle the preview content
          if (previewContent.classList.contains('hidden')) {
            // Show the preview
            previewContent.classList.remove('hidden');
            previewContent.style.maxHeight = '0';
            previewContent.style.opacity = '0';

            // Animate the expansion
            setTimeout(function() {
              previewContent.style.transition = 'max-height 0.5s ease, opacity 0.5s ease';
              previewContent.style.maxHeight = previewContent.scrollHeight + 'px';
              previewContent.style.opacity = '1';

              // Scroll to ensure the expanded content is visible
              const cardRect = card.getBoundingClientRect();
              const scrollPosition = window.scrollY + cardRect.top - 100; // Add some padding
              window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
              });
            }, 10);

            // Change button text
            previewButton.textContent = 'Hide preview ↑';
          } else {
            // Hide the preview
            previewContent.style.maxHeight = '0';
            previewContent.style.opacity = '0';

            // After animation completes, hide the element
            setTimeout(function() {
              previewContent.classList.add('hidden');
              previewContent.style.maxHeight = '';
              previewContent.style.opacity = '';
            }, 500);

            // Change button text back
            previewButton.textContent = buttonText;
          }
        });
      }
    }

    // Setup Medical Summary Preview
    setupExpandablePreview(
      'preview-medical-summary',
      'medical-summary-preview',
      'medical-summary-card',
      'Preview a medical summary →'
    );

    // Setup Damages Breakdown Preview
    setupExpandablePreview(
      'preview-damages-breakdown',
      'damages-breakdown-preview',
      'damages-calculation-card',
      'Preview a damages breakdown →'
    );

    // Setup Injury Analysis Preview
    setupExpandablePreview(
      'preview-injury-analysis',
      'injury-analysis-preview',
      'visual-evidence-card',
      'Preview injury analysis →'
    );

    // Setup Demand Letter Preview
    setupExpandablePreview(
      'preview-demand-letter',
      'demand-letter-preview',
      'demand-letter-card',
      'Preview a demand letter →'
    );
  });

  // Parallax Effect
  window.addEventListener('scroll', function() {
    const parallaxSections = document.querySelectorAll('.parallax-section');

    parallaxSections.forEach(section => {
      const distance = window.scrollY - section.offsetTop;
      const isInView = window.scrollY + window.innerHeight > section.offsetTop &&
                       window.scrollY < section.offsetTop + section.offsetHeight;

      if (isInView) {
        // Parallax for layers
        const layers = section.querySelectorAll('.parallax-layer');
        layers.forEach(layer => {
          const speed = layer.getAttribute('data-speed') || 0.05;
          layer.style.transform = `translateY(${distance * speed}px)`;
        });

        // Parallax for individual elements
        const elements = section.querySelectorAll('.parallax-element');
        elements.forEach(element => {
          const speed = element.getAttribute('data-speed') || 0.1;
          const translateY = distance * speed;
          element.style.transform = element.style.transform
            ? element.style.transform.replace(/translateY\([^)]+\)/, '') + ` translateY(${translateY}px)`
            : `translateY(${translateY}px)`;
        });
      }
    });
  });
</script>

<!-- Add keyframes for animations -->
<style>
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  .float-animation {
    animation: float 6s ease-in-out infinite;
  }
</style>
</body>
</html>
